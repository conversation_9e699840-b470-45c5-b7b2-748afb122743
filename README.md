# Cats State Project

A basic Scala sbt project demonstrating the use of the Cats functional programming library, with a focus on the State monad.

## Project Structure

- `build.sbt` - Project configuration with Cats core dependency
- `src/main/scala/Main.scala` - Main application with examples of:
  - State monad for stack operations
  - Cats syntax for Option and Either
- `src/test/scala/MainSpec.scala` - Unit tests for the examples

## Dependencies

- **Cats Core 2.10.0** - Functional programming library for Scala
- **ScalaTest 3.2.17** - Testing framework (test scope)

## Running the Project

### Compile the project
```bash
sbt compile
```

### Run the main application
```bash
sbt run
```

### Run tests
```bash
sbt test
```

### Start sbt shell
```bash
sbt
```

## Examples Included

1. **State Monad**: Stack operations (push, pop, peek) using `cats.data.State`
2. **Option Syntax**: Using cats syntax extensions for Option
3. **Either Syntax**: Using cats syntax extensions for Either with `.asRight` and `.asLeft`

## Learning Resources

- [Cats Documentation](https://typelevel.org/cats/)
- [Cats State Monad Guide](https://typelevel.org/cats/datatypes/state.html)
