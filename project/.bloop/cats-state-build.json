{"version": "1.4.0", "project": {"name": "cats-state-build", "directory": "/Users/<USER>/SourceCode/cats-state/project", "workspaceDir": "/Users/<USER>/SourceCode/cats-state/project", "sources": ["/Users/<USER>/SourceCode/cats-state/project/src/main/scala", "/Users/<USER>/SourceCode/cats-state/project/src/main/scala-2.12", "/Users/<USER>/SourceCode/cats-state/project/src/main/scala-2", "/Users/<USER>/SourceCode/cats-state/project/src/main/java", "/Users/<USER>/SourceCode/cats-state/project/src/main/scala-sbt-1.0", "/Users/<USER>/SourceCode/cats-state/project/target/scala-2.12/sbt-1.0/src_managed/main"], "dependencies": [], "classpath": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.10.5/sbt-1.10.5.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.10.5/main_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.1/io_2.12-1.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.10.5/logic_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.10.5/actions_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.10.5/main-settings_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.10.5/run_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.10.5/command_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.10.5/collections_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.10.5/scripted-plugin_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.10.5/zinc-lm-integration_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.10.5/util-logging_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.4/launcher-interface-1.4.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.5/lm-coursier-shaded_2.12-2.1.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.10.2/librarymanagement-core_2.12-1.10.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.10.2/librarymanagement-ivy_2.12-1.10.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.4/compiler-interface-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.4/zinc-compile_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.10.5/util-relation_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.10.5/completion_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.10.5/task-system_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.10.5/tasks_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.10.5/testing_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.10.5/util-tracking_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.10.1/sjson-new-scalajson_2.12-0.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.27.1/jline-terminal-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.4/zinc-classpath_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.4/zinc-apiinfo_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.4/zinc_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.10.5/core-macros_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.10.5/util-cache_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.10.5/util-control_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.10.5/protocol_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.10.1/sjson-new-core_2.12-0.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.10.5/util-position_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.4/zinc-compile-core_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.10.5/util-interface-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79/jline-2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.27.1/jline-terminal-jni-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.27.1/jline-native-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.12.0/scala-collection-compat_2.12-2.12.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.17/jsch-0.2.17.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.27.1/jline-reader-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.27.1/jline-builtins-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.10.5/test-agent-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/1.3.2/shaded-jawn-parser_2.12-1.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.4/compiler-bridge_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.4/zinc-classfile_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.4/zinc-core_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.4/zinc-persist_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.10.1/sjson-new-murmurhash_2.12-0.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.3/ipcsocket-1.6.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.16/zero-allocation-hashing-0.16.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.0/jansi-2.4.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.27.1/jline-style-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.4/zinc-persist-core-assembly-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.0/jna-5.12.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.12.0/jna-platform-5.12.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2.jar"], "out": "/Users/<USER>/SourceCode/cats-state/project/.bloop/cats-state-build", "classesDir": "/Users/<USER>/SourceCode/cats-state/project/.bloop/cats-state-build/scala-2.12/sbt-1.0/classes", "resources": ["/Users/<USER>/SourceCode/cats-state/project/src/main/resources", "/Users/<USER>/SourceCode/cats-state/project/target/scala-2.12/sbt-1.0/resource_managed/main"], "scala": {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.12.20", "options": ["-deprecation", "-Wconf:cat=unused-nowarn:s", "-Wconf:cat=unused-nowarn:s", "-Xsource:3"], "jars": ["/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-xml_2.12-2.3.0.jar"], "setup": {"order": "mixed", "addLibraryToBootClasspath": true, "addCompilerToClasspath": false, "addExtraJarsToClasspath": false, "manageBootClasspath": true, "filterLibraryFromClasspath": true}}, "java": {"options": []}, "sbt": {"sbtVersion": "1.10.5", "autoImports": ["import _root_.scala.xml.{TopScope=>$scope}", "import _root_.sbt._", "import _root_.sbt.Keys._", "import _root_.sbt.nio.Keys._", "import _root_.sbt.ScriptedPlugin.autoImport._, _root_.sbt.plugins.JUnitXmlReportPlugin.autoImport._, _root_.sbt.plugins.MiniDependencyTreePlugin.autoImport._, _root_.bloop.integrations.sbt.BloopPlugin.autoImport._", "import _root_.sbt.plugins.IvyPlugin, _root_.sbt.plugins.JvmPlugin, _root_.sbt.plugins.CorePlugin, _root_.sbt.ScriptedPlugin, _root_.sbt.plugins.SbtPlugin, _root_.sbt.plugins.SemanticdbPlugin, _root_.sbt.plugins.JUnitXmlReportPlugin, _root_.sbt.plugins.Giter8TemplatePlugin, _root_.sbt.plugins.MiniDependencyTreePlugin, _root_.bloop.integrations.sbt.BloopPlugin"]}, "test": {"frameworks": [{"names": ["org.scalacheck.ScalaCheckFramework"]}, {"names": ["org.specs2.runner.Specs2Framework", "org.specs2.runner.SpecsFramework"]}, {"names": ["org.specs.runner.SpecsFramework"]}, {"names": ["org.scalatest.tools.Framework", "org.scalatest.tools.ScalaTestFramework"]}, {"names": ["com.novocode.junit.JUnitFramework"]}, {"names": ["munit.Framework"]}, {"names": ["zio.test.sbt.ZTestFramework"]}, {"names": ["weaver.framework.CatsEffect"]}, {"names": ["hedgehog.sbt.Framework"]}], "options": {"excludes": [], "arguments": []}}, "platform": {"name": "jvm", "config": {"home": "/Users/<USER>/Library/Caches/Coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_aarch64_mac_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6/Contents/Home", "options": ["-Duser.dir=/Users/<USER>/SourceCode/cats-state/project"]}, "mainClass": [], "runtimeConfig": {"home": "/Users/<USER>/Library/Caches/Coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_aarch64_mac_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6/Contents/Home", "options": ["-Duser.dir=/Users/<USER>/SourceCode/cats-state/project"]}, "classpath": ["/Users/<USER>/SourceCode/cats-state/project/.bloop/cats-state-build/scala-2.12/sbt-1.0/classes", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]}, "resolution": {"modules": [{"organization": "ch.epfl.scala", "name": "sbt-bloop", "version": "2.0.10", "configurations": "default", "artifacts": [{"name": "sbt-bloop", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"}, {"name": "sbt-bloop", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"}]}, {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "version": "2.3.2", "configurations": "default", "artifacts": [{"name": "bloop-config_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"}, {"name": "bloop-config_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"}]}, {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "version": "2.13.5.2", "configurations": "default", "artifacts": [{"name": "jsoniter-scala-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2.jar"}, {"name": "jsoniter-scala-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "version": "0.1.12", "configurations": "default", "artifacts": [{"name": "unroll-annotation_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"}, {"name": "unroll-annotation_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.12.20", "configurations": "default", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.20/scala-library-2.12.20-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "sbt", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "sbt", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.10.5/sbt-1.10.5.jar"}, {"name": "sbt", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.10.5/sbt-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "main_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "main_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.10.5/main_2.12-1.10.5.jar"}, {"name": "main_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.10.5/main_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "io_2.12", "version": "1.10.1", "configurations": "default", "artifacts": [{"name": "io_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.1/io_2.12-1.10.1.jar"}, {"name": "io_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.1/io_2.12-1.10.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "logic_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "logic_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.10.5/logic_2.12-1.10.5.jar"}, {"name": "logic_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.10.5/logic_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "actions_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "actions_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.10.5/actions_2.12-1.10.5.jar"}, {"name": "actions_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.10.5/actions_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "main-settings_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "main-settings_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.10.5/main-settings_2.12-1.10.5.jar"}, {"name": "main-settings_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.10.5/main-settings_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "run_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "run_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.10.5/run_2.12-1.10.5.jar"}, {"name": "run_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.10.5/run_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "command_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "command_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.10.5/command_2.12-1.10.5.jar"}, {"name": "command_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.10.5/command_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "collections_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "collections_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.10.5/collections_2.12-1.10.5.jar"}, {"name": "collections_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.10.5/collections_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "scripted-plugin_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.10.5/scripted-plugin_2.12-1.10.5.jar"}, {"name": "scripted-plugin_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.10.5/scripted-plugin_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-lm-integration_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "zinc-lm-integration_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.10.5/zinc-lm-integration_2.12-1.10.5.jar"}, {"name": "zinc-lm-integration_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.10.5/zinc-lm-integration_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-logging_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.10.5/util-logging_2.12-1.10.5.jar"}, {"name": "util-logging_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.10.5/util-logging_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "version": "2.3.0", "configurations": "default", "artifacts": [{"name": "scala-xml_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar"}, {"name": "scala-xml_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "launcher-interface", "version": "1.4.4", "configurations": "default", "artifacts": [{"name": "launcher-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.4/launcher-interface-1.4.4.jar"}, {"name": "launcher-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.4/launcher-interface-1.4.4-sources.jar"}]}, {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "version": "2.8.5", "configurations": "default", "artifacts": [{"name": "caffeine", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5.jar"}, {"name": "caffeine", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar"}]}, {"organization": "io.get-coursier", "name": "lm-coursier-shaded_2.12", "version": "2.1.5", "configurations": "default", "artifacts": [{"name": "lm-coursier-shaded_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.5/lm-coursier-shaded_2.12-2.1.5.jar"}, {"name": "lm-coursier-shaded_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.5/lm-coursier-shaded_2.12-2.1.5-sources.jar"}]}, {"organization": "org.apache.logging.log4j", "name": "log4j-api", "version": "2.17.1", "configurations": "default", "artifacts": [{"name": "log4j-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar"}, {"name": "log4j-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar"}]}, {"organization": "org.apache.logging.log4j", "name": "log4j-core", "version": "2.17.1", "configurations": "default", "artifacts": [{"name": "log4j-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar"}, {"name": "log4j-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar"}]}, {"organization": "org.apache.logging.log4j", "name": "log4j-slf4j-impl", "version": "2.17.1", "configurations": "default", "artifacts": [{"name": "log4j-slf4j-impl", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar"}, {"name": "log4j-slf4j-impl", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "version": "1.10.2", "configurations": "default", "artifacts": [{"name": "librarymanagement-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.10.2/librarymanagement-core_2.12-1.10.2.jar"}, {"name": "librarymanagement-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.10.2/librarymanagement-core_2.12-1.10.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "version": "1.10.2", "configurations": "default", "artifacts": [{"name": "librarymanagement-ivy_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.10.2/librarymanagement-ivy_2.12-1.10.2.jar"}, {"name": "librarymanagement-ivy_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.10.2/librarymanagement-ivy_2.12-1.10.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "compiler-interface", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "compiler-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.4/compiler-interface-1.10.4.jar"}, {"name": "compiler-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.4/compiler-interface-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-compile_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-compile_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.4/zinc-compile_2.12-1.10.4.jar"}, {"name": "zinc-compile_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.4/zinc-compile_2.12-1.10.4-sources.jar"}]}, {"organization": "com.swoval", "name": "file-tree-views", "version": "2.1.12", "configurations": "default", "artifacts": [{"name": "file-tree-views", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12.jar"}, {"name": "file-tree-views", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-relation_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-relation_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.10.5/util-relation_2.12-1.10.5.jar"}, {"name": "util-relation_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.10.5/util-relation_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "completion_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "completion_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.10.5/completion_2.12-1.10.5.jar"}, {"name": "completion_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.10.5/completion_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "task-system_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "task-system_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.10.5/task-system_2.12-1.10.5.jar"}, {"name": "task-system_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.10.5/task-system_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "tasks_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "tasks_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.10.5/tasks_2.12-1.10.5.jar"}, {"name": "tasks_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.10.5/tasks_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "testing_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "testing_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.10.5/testing_2.12-1.10.5.jar"}, {"name": "testing_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.10.5/testing_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-tracking_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-tracking_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.10.5/util-tracking_2.12-1.10.5.jar"}, {"name": "util-tracking_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.10.5/util-tracking_2.12-1.10.5-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "sjson-new-scal<PERSON>son_2.12", "version": "0.10.1", "configurations": "default", "artifacts": [{"name": "sjson-new-scal<PERSON>son_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.10.1/sjson-new-scalajson_2.12-0.10.1.jar"}, {"name": "sjson-new-scal<PERSON>son_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.10.1/sjson-new-scalajson_2.12-0.10.1-sources.jar"}]}, {"organization": "org.jline", "name": "jline-terminal", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-terminal", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.27.1/jline-terminal-3.27.1.jar"}, {"name": "jline-terminal", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.27.1/jline-terminal-3.27.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-classpath_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-classpath_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.4/zinc-classpath_2.12-1.10.4.jar"}, {"name": "zinc-classpath_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.4/zinc-classpath_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-apiinfo_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-apiinfo_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.4/zinc-apiinfo_2.12-1.10.4.jar"}, {"name": "zinc-apiinfo_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.4/zinc-apiinfo_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.4/zinc_2.12-1.10.4.jar"}, {"name": "zinc_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.4/zinc_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "core-macros_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "core-macros_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.10.5/core-macros_2.12-1.10.5.jar"}, {"name": "core-macros_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.10.5/core-macros_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-cache_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-cache_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.10.5/util-cache_2.12-1.10.5.jar"}, {"name": "util-cache_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.10.5/util-cache_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-control_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-control_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.10.5/util-control_2.12-1.10.5.jar"}, {"name": "util-control_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.10.5/util-control_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "protocol_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "protocol_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.10.5/protocol_2.12-1.10.5.jar"}, {"name": "protocol_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.10.5/protocol_2.12-1.10.5-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "sjson-new-core_2.12", "version": "0.10.1", "configurations": "default", "artifacts": [{"name": "sjson-new-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.10.1/sjson-new-core_2.12-0.10.1.jar"}, {"name": "sjson-new-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.10.1/sjson-new-core_2.12-0.10.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "template-resolver", "version": "0.1", "configurations": "default", "artifacts": [{"name": "template-resolver", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1.jar"}, {"name": "template-resolver", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-position_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.10.5/util-position_2.12-1.10.5.jar"}, {"name": "util-position_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.10.5/util-position_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-compile-core_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-compile-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.4/zinc-compile-core_2.12-1.10.4.jar"}, {"name": "zinc-compile-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.4/zinc-compile-core_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-interface", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "util-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.10.5/util-interface-1.10.5.jar"}, {"name": "util-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.10.5/util-interface-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt.jline", "name": "jline", "version": "2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79", "configurations": "default", "artifacts": [{"name": "jline", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79/jline-2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79.jar"}, {"name": "jline", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79/jline-2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79-sources.jar"}]}, {"organization": "org.jline", "name": "jline-terminal-jni", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-terminal-jni", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.27.1/jline-terminal-jni-3.27.1.jar"}, {"name": "jline-terminal-jni", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.27.1/jline-terminal-jni-3.27.1-sources.jar"}]}, {"organization": "org.jline", "name": "jline-native", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-native", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.27.1/jline-native-3.27.1.jar"}, {"name": "jline-native", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.27.1/jline-native-3.27.1-sources.jar"}]}, {"organization": "com.lmax", "name": "disruptor", "version": "3.4.2", "configurations": "default", "artifacts": [{"name": "disruptor", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar"}, {"name": "disruptor", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-reflect", "version": "2.12.20", "configurations": "default", "artifacts": [{"name": "scala-reflect", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar"}, {"name": "scala-reflect", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.20/scala-reflect-2.12.20-sources.jar"}]}, {"organization": "org.checkerframework", "name": "checker-qual", "version": "3.4.1", "configurations": "default", "artifacts": [{"name": "checker-qual", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1.jar"}, {"name": "checker-qual", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar"}]}, {"organization": "com.google.errorprone", "name": "error_prone_annotations", "version": "2.4.0", "configurations": "default", "artifacts": [{"name": "error_prone_annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.jar"}, {"name": "error_prone_annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.12", "version": "2.12.0", "configurations": "default", "artifacts": [{"name": "scala-collection-compat_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.12.0/scala-collection-compat_2.12-2.12.0.jar"}, {"name": "scala-collection-compat_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.12.0/scala-collection-compat_2.12-2.12.0-sources.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-api", "version": "1.7.36", "configurations": "default", "artifacts": [{"name": "slf4j-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar"}, {"name": "slf4j-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.12.20", "configurations": "default", "artifacts": [{"name": "scala-compiler", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar"}, {"name": "scala-compiler", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.20/scala-compiler-2.12.20-sources.jar"}]}, {"organization": "com.github.mwiede", "name": "jsch", "version": "0.2.17", "configurations": "default", "artifacts": [{"name": "jsch", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.17/jsch-0.2.17.jar"}, {"name": "jsch", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.17/jsch-0.2.17-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "gigahorse-apache-http_2.12", "version": "0.7.0", "configurations": "default", "artifacts": [{"name": "gigahorse-apache-http_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0.jar"}, {"name": "gigahorse-apache-http_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar"}]}, {"organization": "org.scala-sbt.ivy", "name": "ivy", "version": "2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2", "configurations": "default", "artifacts": [{"name": "ivy", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2.jar"}, {"name": "ivy", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar"}]}, {"organization": "org.jline", "name": "jline-reader", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-reader", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.27.1/jline-reader-3.27.1.jar"}, {"name": "jline-reader", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.27.1/jline-reader-3.27.1-sources.jar"}]}, {"organization": "org.jline", "name": "jline-builtins", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-builtins", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.27.1/jline-builtins-3.27.1.jar"}, {"name": "jline-builtins", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.27.1/jline-builtins-3.27.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "test-agent", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "test-agent", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.10.5/test-agent-1.10.5.jar"}, {"name": "test-agent", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.10.5/test-agent-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "test-interface", "version": "1.0", "configurations": "default", "artifacts": [{"name": "test-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"}, {"name": "test-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "shaded-s<PERSON><PERSON>son_2.12", "version": "1.0.0-M4", "configurations": "default", "artifacts": [{"name": "shaded-s<PERSON><PERSON>son_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4.jar"}, {"name": "shaded-s<PERSON><PERSON>son_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "shaded-jawn-parser_2.12", "version": "1.3.2", "configurations": "default", "artifacts": [{"name": "shaded-jawn-parser_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/1.3.2/shaded-jawn-parser_2.12-1.3.2.jar"}, {"name": "shaded-jawn-parser_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/1.3.2/shaded-jawn-parser_2.12-1.3.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "compiler-bridge_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "compiler-bridge_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.4/compiler-bridge_2.12-1.10.4.jar"}, {"name": "compiler-bridge_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.4/compiler-bridge_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-classfile_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-classfile_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.4/zinc-classfile_2.12-1.10.4.jar"}, {"name": "zinc-classfile_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.4/zinc-classfile_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-core_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.4/zinc-core_2.12-1.10.4.jar"}, {"name": "zinc-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.4/zinc-core_2.12-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-persist_2.12", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-persist_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.4/zinc-persist_2.12-1.10.4.jar"}, {"name": "zinc-persist_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.4/zinc-persist_2.12-1.10.4-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "sjson-new-murmurhash_2.12", "version": "0.10.1", "configurations": "default", "artifacts": [{"name": "sjson-new-murmurhash_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.10.1/sjson-new-murmurhash_2.12-0.10.1.jar"}, {"name": "sjson-new-murmurhash_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.10.1/sjson-new-murmurhash_2.12-0.10.1-sources.jar"}]}, {"organization": "org.scala-sbt.ipcsocket", "name": "ipcsocket", "version": "1.6.3", "configurations": "default", "artifacts": [{"name": "ipcsocket", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.3/ipcsocket-1.6.3.jar"}, {"name": "ipcsocket", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.3/ipcsocket-1.6.3-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-parser-combinators_2.12", "version": "1.1.2", "configurations": "default", "artifacts": [{"name": "scala-parser-combinators_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2.jar"}, {"name": "scala-parser-combinators_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar"}]}, {"organization": "net.openhft", "name": "zero-allocation-hashing", "version": "0.16", "configurations": "default", "artifacts": [{"name": "zero-allocation-hashing", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.16/zero-allocation-hashing-0.16.jar"}, {"name": "zero-allocation-hashing", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.16/zero-allocation-hashing-0.16-sources.jar"}]}, {"organization": "org.fusesource.jansi", "name": "jansi", "version": "2.4.0", "configurations": "default", "artifacts": [{"name": "jansi", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.0/jansi-2.4.0.jar"}, {"name": "jansi", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.0/jansi-2.4.0-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "gigahorse-core_2.12", "version": "0.7.0", "configurations": "default", "artifacts": [{"name": "gigahorse-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0.jar"}, {"name": "gigahorse-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "shaded-apache-httpasyncclient", "version": "0.7.0", "configurations": "default", "artifacts": [{"name": "shaded-apache-httpasyncclient", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0.jar"}, {"name": "shaded-apache-httpasyncclient", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar"}]}, {"organization": "org.jline", "name": "jline-style", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-style", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.27.1/jline-style-3.27.1.jar"}, {"name": "jline-style", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.27.1/jline-style-3.27.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-persist-core-assembly", "version": "1.10.4", "configurations": "default", "artifacts": [{"name": "zinc-persist-core-assembly", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.4/zinc-persist-core-assembly-1.10.4.jar"}, {"name": "zinc-persist-core-assembly", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.4/zinc-persist-core-assembly-1.10.4-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "sbinary_2.12", "version": "0.5.1", "configurations": "default", "artifacts": [{"name": "sbinary_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1.jar"}, {"name": "sbinary_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar"}]}, {"organization": "net.java.dev.jna", "name": "jna", "version": "5.12.0", "configurations": "default", "artifacts": [{"name": "jna", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.0/jna-5.12.0.jar"}, {"name": "jna", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.0/jna-5.12.0-sources.jar"}]}, {"organization": "net.java.dev.jna", "name": "jna-platform", "version": "5.12.0", "configurations": "default", "artifacts": [{"name": "jna-platform", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.12.0/jna-platform-5.12.0.jar"}, {"name": "jna-platform", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.12.0/jna-platform-5.12.0-sources.jar"}]}, {"organization": "com.typesafe", "name": "ssl-config-core_2.12", "version": "0.6.1", "configurations": "default", "artifacts": [{"name": "ssl-config-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1.jar"}, {"name": "ssl-config-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar"}]}, {"organization": "org.reactivestreams", "name": "reactive-streams", "version": "1.0.3", "configurations": "default", "artifacts": [{"name": "reactive-streams", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar"}, {"name": "reactive-streams", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar"}]}, {"organization": "com.typesafe", "name": "config", "version": "1.4.2", "configurations": "default", "artifacts": [{"name": "config", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2.jar"}, {"name": "config", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.12.20", "configurations": "optional", "artifacts": [{"name": "scala-compiler", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar"}, {"name": "scala-compiler", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.20/scala-compiler-2.12.20-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.12.20", "configurations": "optional", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.20/scala-library-2.12.20-sources.jar"}]}]}, "tags": ["library"]}}