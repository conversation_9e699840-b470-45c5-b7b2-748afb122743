[debug] Created transactional ClassFileManager with tempDir = /Users/<USER>/SourceCode/cats-state/project/target/scala-2.12/sbt-1.0/classes.bak
[debug] About to delete class files:
[debug] We backup class files:
[debug] Created transactional ClassFileManager with tempDir = /Users/<USER>/SourceCode/cats-state/project/target/scala-2.12/sbt-1.0/classes.bak
[debug] Removing the temporary directory used for backing up class files: /Users/<USER>/SourceCode/cats-state/project/target/scala-2.12/sbt-1.0/classes.bak
