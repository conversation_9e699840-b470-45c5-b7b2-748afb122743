{"{\"organization\":\"org.scala-lang\",\"name\":\"scala-library\",\"revision\":\"2.12.20\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/Users/<USER>/SourceCode/cats-state/project/metals.sbt", "range": {"$fields": ["start", "end"], "start": 6, "end": 7}}, "type": "RangePosition"}, "{\"organization\":\"ch.epfl.scala\",\"name\":\"sbt-bloop\",\"revision\":\"2.0.10\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/Users/<USER>/SourceCode/cats-state/project/metals.sbt", "range": {"$fields": ["start", "end"], "start": 6, "end": 7}}, "type": "RangePosition"}}