{"cachedDescriptor": ".", "configurations": [{"configuration": {"name": "compile"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "2.13.5.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "compile-internal"}, "modules": [{"module": {"organization": "org.scala-sbt", "name": "sbt", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/sbt/1.9.7/sbt-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.9.7/sbt-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "main_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "main_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.9.7/main_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.9.7/main_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "io_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "io_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.9.7/io_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.9.7/io_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/io", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "logic_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "logic_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.9.7/logic_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.9.7/logic_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "actions_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "actions_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.9.7/actions_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.9.7/actions_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "main-settings_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "main-settings_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.9.7/main-settings_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.9.7/main-settings_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "run_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "run_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.9.7/run_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.9.7/run_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "command_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "command_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.9.7/command_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.9.7/command_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "collections_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "collections_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.9.7/collections_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.9.7/collections_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scripted-plugin_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.9.7/scripted-plugin_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.9.7/scripted-plugin_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-lm-integration_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-lm-integration_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.9.7/zinc-lm-integration_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.9.7/zinc-lm-integration_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-logging_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-logging_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.9.7/util-logging_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.9.7/util-logging_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "revision": "2.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.2.0/scala-xml_2.12-2.2.0-sources.jar", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.2.0/scala-xml_2.12-2.2.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "launcher-interface", "revision": "1.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "launcher-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.2/launcher-interface-1.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.2/launcher-interface-1.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "2.8.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.get-coursier", "name": "lm-coursier-shaded_2.12", "revision": "2.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "lm-coursier-shaded_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.2/lm-coursier-shaded_2.12-2.1.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.2/lm-coursier-shaded_2.12-2.1.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/coursier/sbt-coursier", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://opensource.org/licenses/Apache-2.0"]], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-api", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-api", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-core", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-core", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-slf4j-impl", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-slf4j-impl", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "revision": "1.9.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "librarymanagement-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.9.3/librarymanagement-core_2.12-1.9.3-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.9.3/librarymanagement-core_2.12-1.9.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/librarymanagement", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "revision": "1.9.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "librarymanagement-ivy_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.9.3/librarymanagement-ivy_2.12-1.9.3-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.9.3/librarymanagement-ivy_2.12-1.9.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/librarymanagement", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-interface", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.5/compiler-interface-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.5/compiler-interface-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-compile_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-compile_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.9.5/zinc-compile_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.9.5/zinc-compile_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.swoval", "name": "file-tree-views", "revision": "2.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "file-tree-views", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/swoval/swoval", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna-platform", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna-platform", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "2.13.5.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-relation_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-relation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.9.7/util-relation_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.9.7/util-relation_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "completion_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "completion_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.9.7/completion_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.9.7/completion_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "task-system_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "task-system_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.9.7/task-system_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.9.7/task-system_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "tasks_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "tasks_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.9.7/tasks_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.9.7/tasks_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "testing_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "testing_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.9.7/testing_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.9.7/testing_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-tracking_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-tracking_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.9.7/util-tracking_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.9.7/util-tracking_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-scal<PERSON>son_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-scal<PERSON>son_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.9.1/sjson-new-scalajson_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.9.1/sjson-new-scalajson_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal/3.19.0/jline-terminal-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.19.0/jline-terminal-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-classpath_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-classpath_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.9.5/zinc-classpath_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.9.5/zinc-classpath_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-apiinfo_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-apiinfo_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.9.5/zinc-apiinfo_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.9.5/zinc-apiinfo_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.9.5/zinc_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.9.5/zinc_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "core-macros_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "core-macros_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.9.7/core-macros_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.9.7/core-macros_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-cache_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-cache_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.9.7/util-cache_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.9.7/util-cache_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-control_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-control_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.9.7/util-control_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.9.7/util-control_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "protocol_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "protocol_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.9.7/protocol_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.9.7/protocol_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-core_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.9.1/sjson-new-core_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.9.1/sjson-new-core_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "template-resolver", "revision": "0.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "template-resolver", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/template-resolver", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-position_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-position_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.9.7/util-position_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.9.7/util-position_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-compile-core_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-compile-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.9.5/zinc-compile-core_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.9.5/zinc-compile-core_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-interface", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.7/util-interface-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.7/util-interface-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt.jline", "name": "jline", "revision": "2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/jline2", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jna", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.19.0/jline-terminal-jna-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.19.0/jline-terminal-jna-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jansi", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jansi", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jansi/3.19.0/jline-terminal-jansi-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jansi/3.19.0/jline-terminal-jansi-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "com.lmax", "name": "disruptor", "revision": "3.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "disruptor", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://lmax-exchange.github.com/disruptor", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.checkerframework", "name": "checker-qual", "revision": "3.4.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "checker-qual", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://checkerframework.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The MIT License", "http://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.4.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.12", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.10.0/scala-collection-compat_2.12-2.10.0-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.10.0/scala-collection-compat_2.12-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "1.7.36", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.jcraft", "name": "jsch", "revision": "0.1.54", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsch", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.jcraft.com/jsch/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Revised BSD", "http://www.jcraft.com/jsch/LICENSE.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "gigahorse-apache-http_2.12", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "gigahorse-apache-http_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt.ivy", "name": "ivy", "revision": "2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ivy", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/ivy", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/ivy/blob/2.3.x-sbt/LICENSE"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-reader", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-reader", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-reader/3.19.0/jline-reader-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.19.0/jline-reader-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-builtins", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-builtins", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-builtins/3.19.0/jline-builtins-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.19.0/jline-builtins-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-agent", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-agent", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-agent/1.9.7/test-agent-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.9.7/test-agent-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-jawn-parser_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-jawn-parser_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/0.9.1/shaded-jawn-parser_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/0.9.1/shaded-jawn-parser_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-s<PERSON><PERSON>son_2.12", "revision": "1.0.0-M4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-s<PERSON><PERSON>son_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/mdedetrich/scalajson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3 Clause", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-bridge_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-bridge_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.9.5/compiler-bridge_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.9.5/compiler-bridge_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-classfile_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-classfile_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.9.5/zinc-classfile_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.9.5/zinc-classfile_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-core_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.9.5/zinc-core_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.9.5/zinc-core_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-persist_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-persist_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.9.5/zinc-persist_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.9.5/zinc-persist_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-murmurhash_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-murmurhash_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.9.1/sjson-new-murmurhash_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.9.1/sjson-new-murmurhash_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt.ipcsocket", "name": "ipcsocket", "revision": "1.6.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ipcsocket", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.2/ipcsocket-1.6.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.2/ipcsocket-1.6.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/ipcsocket", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-parser-combinators_2.12", "revision": "1.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-parser-combinators_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "net.openhft", "name": "zero-allocation-hashing", "revision": "0.10.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zero-allocation-hashing", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.10.1/zero-allocation-hashing-0.10.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.10.1/zero-allocation-hashing-0.10.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/OpenHFT/Zero-Allocation-Hashing", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.fusesource.jansi", "name": "jansi", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jansi", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.1.0/jansi-2.1.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.1.0/jansi-2.1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://fusesource.github.io/jansi", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "gigahorse-core_2.12", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "gigahorse-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-apache-httpasyncclient", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-apache-httpasyncclient", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-style", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-style", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-style/3.19.0/jline-style-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.19.0/jline-style-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-persist-core-assembly", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-persist-core-assembly", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.9.5/zinc-persist-core-assembly-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.9.5/zinc-persist-core-assembly-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "sbinary_2.12", "revision": "0.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbinary_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbinary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://github.com/sbt/sbinary/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.12", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["CC0", "http://creativecommons.org/publicdomain/zero/1.0/"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "docs"}, "modules": [], "details": []}, {"configuration": {"name": "optional"}, "modules": [], "details": []}, {"configuration": {"name": "plugin"}, "modules": [], "details": []}, {"configuration": {"name": "pom"}, "modules": [], "details": []}, {"configuration": {"name": "provided"}, "modules": [{"module": {"organization": "org.scala-sbt", "name": "sbt", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/sbt/1.9.7/sbt-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.9.7/sbt-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "main_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "main_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.9.7/main_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.9.7/main_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "io_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "io_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.9.7/io_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.9.7/io_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/io", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "logic_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "logic_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.9.7/logic_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.9.7/logic_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "actions_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "actions_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.9.7/actions_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.9.7/actions_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "main-settings_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "main-settings_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.9.7/main-settings_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.9.7/main-settings_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "run_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "run_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.9.7/run_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.9.7/run_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "command_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "command_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.9.7/command_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.9.7/command_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "collections_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "collections_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.9.7/collections_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.9.7/collections_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scripted-plugin_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.9.7/scripted-plugin_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.9.7/scripted-plugin_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-lm-integration_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-lm-integration_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.9.7/zinc-lm-integration_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.9.7/zinc-lm-integration_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-logging_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-logging_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.9.7/util-logging_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.9.7/util-logging_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "revision": "2.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.2.0/scala-xml_2.12-2.2.0-sources.jar", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.2.0/scala-xml_2.12-2.2.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "launcher-interface", "revision": "1.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "launcher-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.2/launcher-interface-1.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.2/launcher-interface-1.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "2.8.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.get-coursier", "name": "lm-coursier-shaded_2.12", "revision": "2.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "lm-coursier-shaded_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.2/lm-coursier-shaded_2.12-2.1.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.2/lm-coursier-shaded_2.12-2.1.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/coursier/sbt-coursier", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://opensource.org/licenses/Apache-2.0"]], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-api", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-api", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-core", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-core", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-slf4j-impl", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-slf4j-impl", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "revision": "1.9.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "librarymanagement-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.9.3/librarymanagement-core_2.12-1.9.3-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.9.3/librarymanagement-core_2.12-1.9.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/librarymanagement", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "revision": "1.9.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "librarymanagement-ivy_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.9.3/librarymanagement-ivy_2.12-1.9.3-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.9.3/librarymanagement-ivy_2.12-1.9.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/librarymanagement", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-interface", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.5/compiler-interface-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.5/compiler-interface-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-compile_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-compile_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.9.5/zinc-compile_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.9.5/zinc-compile_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.swoval", "name": "file-tree-views", "revision": "2.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "file-tree-views", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/swoval/swoval", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna-platform", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna-platform", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-relation_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-relation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.9.7/util-relation_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.9.7/util-relation_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "completion_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "completion_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.9.7/completion_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.9.7/completion_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "task-system_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "task-system_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.9.7/task-system_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.9.7/task-system_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "tasks_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "tasks_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.9.7/tasks_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.9.7/tasks_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "testing_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "testing_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.9.7/testing_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.9.7/testing_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-tracking_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-tracking_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.9.7/util-tracking_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.9.7/util-tracking_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-scal<PERSON>son_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-scal<PERSON>son_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.9.1/sjson-new-scalajson_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.9.1/sjson-new-scalajson_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal/3.19.0/jline-terminal-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.19.0/jline-terminal-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-classpath_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-classpath_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.9.5/zinc-classpath_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.9.5/zinc-classpath_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-apiinfo_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-apiinfo_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.9.5/zinc-apiinfo_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.9.5/zinc-apiinfo_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.9.5/zinc_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.9.5/zinc_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "core-macros_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "core-macros_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.9.7/core-macros_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.9.7/core-macros_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-cache_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-cache_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.9.7/util-cache_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.9.7/util-cache_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-control_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-control_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.9.7/util-control_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.9.7/util-control_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "protocol_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "protocol_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.9.7/protocol_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.9.7/protocol_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-core_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.9.1/sjson-new-core_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.9.1/sjson-new-core_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "template-resolver", "revision": "0.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "template-resolver", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/template-resolver", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-position_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-position_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.9.7/util-position_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.9.7/util-position_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-compile-core_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-compile-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.9.5/zinc-compile-core_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.9.5/zinc-compile-core_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-interface", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.7/util-interface-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.7/util-interface-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt.jline", "name": "jline", "revision": "2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/jline2", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jna", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.19.0/jline-terminal-jna-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.19.0/jline-terminal-jna-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jansi", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jansi", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jansi/3.19.0/jline-terminal-jansi-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jansi/3.19.0/jline-terminal-jansi-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "com.lmax", "name": "disruptor", "revision": "3.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "disruptor", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://lmax-exchange.github.com/disruptor", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.checkerframework", "name": "checker-qual", "revision": "3.4.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "checker-qual", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://checkerframework.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The MIT License", "http://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.4.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.12", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.10.0/scala-collection-compat_2.12-2.10.0-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.10.0/scala-collection-compat_2.12-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "1.7.36", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.jcraft", "name": "jsch", "revision": "0.1.54", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsch", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.jcraft.com/jsch/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Revised BSD", "http://www.jcraft.com/jsch/LICENSE.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "gigahorse-apache-http_2.12", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "gigahorse-apache-http_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt.ivy", "name": "ivy", "revision": "2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ivy", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/ivy", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/ivy/blob/2.3.x-sbt/LICENSE"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-reader", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-reader", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-reader/3.19.0/jline-reader-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.19.0/jline-reader-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-builtins", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-builtins", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-builtins/3.19.0/jline-builtins-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.19.0/jline-builtins-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-agent", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-agent", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-agent/1.9.7/test-agent-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.9.7/test-agent-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-jawn-parser_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-jawn-parser_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/0.9.1/shaded-jawn-parser_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/0.9.1/shaded-jawn-parser_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-s<PERSON><PERSON>son_2.12", "revision": "1.0.0-M4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-s<PERSON><PERSON>son_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/mdedetrich/scalajson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3 Clause", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-bridge_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-bridge_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.9.5/compiler-bridge_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.9.5/compiler-bridge_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-classfile_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-classfile_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.9.5/zinc-classfile_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.9.5/zinc-classfile_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-core_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.9.5/zinc-core_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.9.5/zinc-core_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-persist_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-persist_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.9.5/zinc-persist_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.9.5/zinc-persist_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-murmurhash_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-murmurhash_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.9.1/sjson-new-murmurhash_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.9.1/sjson-new-murmurhash_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt.ipcsocket", "name": "ipcsocket", "revision": "1.6.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ipcsocket", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.2/ipcsocket-1.6.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.2/ipcsocket-1.6.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/ipcsocket", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-parser-combinators_2.12", "revision": "1.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-parser-combinators_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "net.openhft", "name": "zero-allocation-hashing", "revision": "0.10.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zero-allocation-hashing", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.10.1/zero-allocation-hashing-0.10.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.10.1/zero-allocation-hashing-0.10.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/OpenHFT/Zero-Allocation-Hashing", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.fusesource.jansi", "name": "jansi", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jansi", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.1.0/jansi-2.1.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.1.0/jansi-2.1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://fusesource.github.io/jansi", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "gigahorse-core_2.12", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "gigahorse-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-apache-httpasyncclient", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-apache-httpasyncclient", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-style", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-style", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-style/3.19.0/jline-style-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.19.0/jline-style-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-persist-core-assembly", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-persist-core-assembly", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.9.5/zinc-persist-core-assembly-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.9.5/zinc-persist-core-assembly-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "sbinary_2.12", "revision": "0.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbinary_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbinary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://github.com/sbt/sbinary/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.12", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["CC0", "http://creativecommons.org/publicdomain/zero/1.0/"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "2.13.5.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime-internal"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "2.13.5.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "scala-doc-tool"}, "modules": [], "details": []}, {"configuration": {"name": "scala-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.18", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-lang", "name": "*", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.1.0/scala-xml_2.12-2.1.0-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.1.0/scala-xml_2.12-2.1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "jline", "name": "jline", "revision": "2.14.6", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/jline/jline/2.14.6/jline-2.14.6-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/jline/jline/2.14.6/jline-2.14.6-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}], "details": []}, {"configuration": {"name": "sources"}, "modules": [], "details": []}, {"configuration": {"name": "test"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "2.13.5.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "test-internal"}, "modules": [{"module": {"organization": "org.scala-sbt", "name": "sbt", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/sbt/1.9.7/sbt-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.9.7/sbt-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.18/scala-library-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "main_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "main_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.9.7/main_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.9.7/main_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "io_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "io_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.9.7/io_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.9.7/io_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/io", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "logic_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "logic_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.9.7/logic_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.9.7/logic_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "actions_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "actions_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.9.7/actions_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.9.7/actions_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "main-settings_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "main-settings_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.9.7/main-settings_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.9.7/main-settings_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "run_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "run_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.9.7/run_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.9.7/run_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "command_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "command_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.9.7/command_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.9.7/command_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "collections_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "collections_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.9.7/collections_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.9.7/collections_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scripted-plugin_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.9.7/scripted-plugin_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.9.7/scripted-plugin_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-lm-integration_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-lm-integration_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.9.7/zinc-lm-integration_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.9.7/zinc-lm-integration_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-logging_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-logging_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.9.7/util-logging_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.9.7/util-logging_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "revision": "2.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.2.0/scala-xml_2.12-2.2.0-sources.jar", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.2.0/scala-xml_2.12-2.2.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "launcher-interface", "revision": "1.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "launcher-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.2/launcher-interface-1.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.2/launcher-interface-1.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "2.8.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.get-coursier", "name": "lm-coursier-shaded_2.12", "revision": "2.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "lm-coursier-shaded_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.2/lm-coursier-shaded_2.12-2.1.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.2/lm-coursier-shaded_2.12-2.1.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/coursier/sbt-coursier", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://opensource.org/licenses/Apache-2.0"]], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-api", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-api", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-core", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-core", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.apache.logging.log4j", "name": "log4j-slf4j-impl", "revision": "2.17.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "log4j-slf4j-impl", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "revision": "1.9.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "librarymanagement-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.9.3/librarymanagement-core_2.12-1.9.3-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.9.3/librarymanagement-core_2.12-1.9.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/librarymanagement", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "revision": "1.9.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "librarymanagement-ivy_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.9.3/librarymanagement-ivy_2.12-1.9.3-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.9.3/librarymanagement-ivy_2.12-1.9.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/librarymanagement", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-interface", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.5/compiler-interface-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.5/compiler-interface-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-compile_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-compile_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.9.5/zinc-compile_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.9.5/zinc-compile_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.swoval", "name": "file-tree-views", "revision": "2.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "file-tree-views", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/swoval/swoval", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna-platform", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna-platform", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "2.13.5.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-relation_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-relation_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.9.7/util-relation_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.9.7/util-relation_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "completion_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "completion_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.9.7/completion_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.9.7/completion_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "task-system_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "task-system_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.9.7/task-system_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.9.7/task-system_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "tasks_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "tasks_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.9.7/tasks_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.9.7/tasks_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "testing_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "testing_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.9.7/testing_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.9.7/testing_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-tracking_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-tracking_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.9.7/util-tracking_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.9.7/util-tracking_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-scal<PERSON>son_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-scal<PERSON>son_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.9.1/sjson-new-scalajson_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.9.1/sjson-new-scalajson_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal/3.19.0/jline-terminal-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.19.0/jline-terminal-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-classpath_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-classpath_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.9.5/zinc-classpath_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.9.5/zinc-classpath_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-apiinfo_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-apiinfo_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.9.5/zinc-apiinfo_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.9.5/zinc-apiinfo_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.9.5/zinc_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.9.5/zinc_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "core-macros_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "core-macros_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.9.7/core-macros_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.9.7/core-macros_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-cache_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-cache_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.9.7/util-cache_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.9.7/util-cache_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-control_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-control_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.9.7/util-control_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.9.7/util-control_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "protocol_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "protocol_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.9.7/protocol_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.9.7/protocol_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-core_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.9.1/sjson-new-core_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.9.1/sjson-new-core_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "template-resolver", "revision": "0.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "template-resolver", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/template-resolver", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-position_2.12", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-position_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.9.7/util-position_2.12-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.9.7/util-position_2.12-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-compile-core_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-compile-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.9.5/zinc-compile-core_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.9.5/zinc-compile-core_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-interface", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.7/util-interface-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.7/util-interface-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt.jline", "name": "jline", "revision": "2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/jline2", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jna", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.19.0/jline-terminal-jna-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.19.0/jline-terminal-jna-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jansi", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jansi", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jansi/3.19.0/jline-terminal-jansi-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jansi/3.19.0/jline-terminal-jansi-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "com.lmax", "name": "disruptor", "revision": "3.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "disruptor", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://lmax-exchange.github.com/disruptor", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.18/scala-reflect-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.18/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.checkerframework", "name": "checker-qual", "revision": "3.4.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "checker-qual", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.4.1/checker-qual-3.4.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://checkerframework.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The MIT License", "http://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.4.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.12", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.10.0/scala-collection-compat_2.12-2.10.0-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.10.0/scala-collection-compat_2.12-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "1.7.36", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.18", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.18/scala-compiler-2.12.18-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.jcraft", "name": "jsch", "revision": "0.1.54", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsch", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/jcraft/jsch/0.1.54/jsch-0.1.54-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.jcraft.com/jsch/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Revised BSD", "http://www.jcraft.com/jsch/LICENSE.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "gigahorse-apache-http_2.12", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "gigahorse-apache-http_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.7.0/gigahorse-apache-http_2.12-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt.ivy", "name": "ivy", "revision": "2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ivy", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/ivy", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/ivy/blob/2.3.x-sbt/LICENSE"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-reader", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-reader", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-reader/3.19.0/jline-reader-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.19.0/jline-reader-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-builtins", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-builtins", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-builtins/3.19.0/jline-builtins-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.19.0/jline-builtins-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-agent", "revision": "1.9.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-agent", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-agent/1.9.7/test-agent-1.9.7-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.9.7/test-agent-1.9.7-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-jawn-parser_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-jawn-parser_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/0.9.1/shaded-jawn-parser_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/0.9.1/shaded-jawn-parser_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-s<PERSON><PERSON>son_2.12", "revision": "1.0.0-M4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-s<PERSON><PERSON>son_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/mdedetrich/scalajson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3 Clause", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-bridge_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-bridge_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.9.5/compiler-bridge_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.9.5/compiler-bridge_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-classfile_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-classfile_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.9.5/zinc-classfile_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.9.5/zinc-classfile_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-core_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.9.5/zinc-core_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.9.5/zinc-core_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-persist_2.12", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-persist_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.9.5/zinc-persist_2.12-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.9.5/zinc-persist_2.12-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "sjson-new-murmurhash_2.12", "revision": "0.9.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sjson-new-murmurhash_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.9.1/sjson-new-murmurhash_2.12-0.9.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.9.1/sjson-new-murmurhash_2.12-0.9.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/sjson-new", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt.ipcsocket", "name": "ipcsocket", "revision": "1.6.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ipcsocket", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.2/ipcsocket-1.6.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.2/ipcsocket-1.6.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/ipcsocket", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-parser-combinators_2.12", "revision": "1.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-parser-combinators_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "net.openhft", "name": "zero-allocation-hashing", "revision": "0.10.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zero-allocation-hashing", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.10.1/zero-allocation-hashing-0.10.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.10.1/zero-allocation-hashing-0.10.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/OpenHFT/Zero-Allocation-Hashing", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.fusesource.jansi", "name": "jansi", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jansi", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.1.0/jansi-2.1.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.1.0/jansi-2.1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://fusesource.github.io/jansi", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "gigahorse-core_2.12", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "gigahorse-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.7.0/gigahorse-core_2.12-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.eed3si9n", "name": "shaded-apache-httpasyncclient", "revision": "0.7.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "shaded-apache-httpasyncclient", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpasyncclient/0.7.0/shaded-apache-httpasyncclient-0.7.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/eed3si9n/gigahorse", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-style", "revision": "3.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-style", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-style/3.19.0/jline-style-3.19.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.19.0/jline-style-3.19.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "zinc-persist-core-assembly", "revision": "1.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "zinc-persist-core-assembly", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.9.5/zinc-persist-core-assembly-1.9.5-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.9.5/zinc-persist-core-assembly-1.9.5-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "sbinary_2.12", "revision": "0.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbinary_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbinary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://github.com/sbt/sbinary/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.12", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.12", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["CC0", "http://creativecommons.org/publicdomain/zero/1.0/"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.2/config-1.4.2-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}], "stats": {"resolveTime": -1, "downloadTime": -1, "downloadSize": -1, "cached": true}, "stamps": {}}