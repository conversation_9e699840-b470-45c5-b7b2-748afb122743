[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.4/compiler-bridge_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.10.5/util-cache_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.10.5/logic_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.10.5/testing_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.4/zinc-apiinfo_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.17/jsch-0.2.17-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.4/compiler-interface-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.5/lm-coursier-shaded_2.12-2.1.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.4/zinc-classpath_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.10.5/collections_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.10.5/zinc-lm-integration_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.4/zinc_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.10.5/util-relation_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.10.5/run_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.1/io_2.12-1.10.1-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.4/zinc-persist-core-assembly-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.10.5/core-macros_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79/jline-2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.10.5/task-system_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.10.5/completion_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.10.5/util-tracking_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.10.2/librarymanagement-ivy_2.12-1.10.2-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.12.0/scala-collection-compat_2.12-2.12.0-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.4/zinc-core_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-interface/1.10.5/util-interface-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.10.5/main-settings_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/test-agent/1.10.5/test-agent-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.4/zinc-compile-core_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.10.5/main_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.10.5/util-control_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.10.5/protocol_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.0/jansi-2.4.0-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.10.5/command_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/sbt/1.10.5/sbt-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.10.5/tasks_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.10.2/librarymanagement-core_2.12-1.10.2-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.10.5/actions_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.10.5/util-logging_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.10.5/scripted-plugin_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.4/zinc-compile_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.4/zinc-persist_2.12-1.10.4-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.10.5/util-position_2.12-1.10.5-sources.jar
[debug] downloaded https://repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.4/zinc-classfile_2.12-1.10.4-sources.jar
