{"cachedDescriptor": ".", "configurations": [{"configuration": {"name": "compile"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "********", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "compile-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "********", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}], "details": []}, {"configuration": {"name": "docs"}, "modules": [], "details": []}, {"configuration": {"name": "optional"}, "modules": [], "details": []}, {"configuration": {"name": "plugin"}, "modules": [], "details": []}, {"configuration": {"name": "pom"}, "modules": [], "details": []}, {"configuration": {"name": "provided"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "********", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime-internal"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "********", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "scala-doc-tool"}, "modules": [], "details": []}, {"configuration": {"name": "scala-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.12.20", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "revision": "2.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-lang", "name": "*", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.12", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://javadoc.io/doc/org.scala-lang.modules/scala-xml_2.13/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "jline", "name": "jline", "revision": "2.14.6", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jline/jline/2.14.6/jline-2.14.6.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/jline/jline/2.14.6/jline-2.14.6.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}], "details": []}, {"configuration": {"name": "sources"}, "modules": [], "details": []}, {"configuration": {"name": "test"}, "modules": [{"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "********", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "test-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.12.20", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.12.20/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "sbt-bloop", "revision": "2.0.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "sbt-bloop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop", "extraAttributes": {"sbtVersion": "1.0", "scalaVersion": "2.12"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "ch.epfl.scala", "name": "bloop-config_2.12", "revision": "2.3.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "bloop-config_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalacenter/bloop-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://spdx.org/licenses/Apache-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.plokhotnyuk.jsoniter-scala", "name": "jsoniter-scala-core_2.12", "revision": "********", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoniter-scala-core_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/********/jsoniter-scala-core_2.12-********.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/plokhotnyuk/jsoniter-scala", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "https://opensource.org/licenses/mit-license.html"]], "callers": []}, {"module": {"organization": "com.lihaoyi", "name": "unroll-annotation_2.12", "revision": "0.1.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [{"organization": "org.scala-sbt", "name": "io_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "sbt", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "artifact": "*", "configurations": [], "crossVersion": {"type": "Disabled"}}], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "unroll-annotation_2.12", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/com-lihaoyi/unroll", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://spdx.org/licenses/MIT.html"]], "callers": []}], "details": []}], "stats": {"resolveTime": -1, "downloadTime": -1, "downloadSize": -1, "cached": false}, "stamps": {}}