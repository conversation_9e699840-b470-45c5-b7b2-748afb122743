/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-xml_2.12-2.3.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-terminal-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/logic_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-terminal-jni-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/collections_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-compile_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/slf4j-api-1.7.36.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/lm-coursier-shaded_2.12-2.1.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-control_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-classpath_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jsch-0.2.23.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/log4j-api-2.17.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/compiler-interface-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/sjson-new-core_2.12-0.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/sbinary_2.12-0.5.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scala-collection-compat_2.12-2.13.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/error_prone_annotations-2.4.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/run_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-classfile_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/gigahorse-core_2.12-0.7.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/io_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/config-1.4.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/test-interface-1.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/main-settings_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/librarymanagement-ivy_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/launcher-interface-1.4.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/gigahorse-apache-http_2.12-0.7.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-core_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/checker-qual-3.4.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-logging_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-tracking_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-interface-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/caffeine-2.8.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/main_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jna-5.12.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/log4j-core-2.17.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/librarymanagement-core_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/core-macros_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/file-tree-views-2.1.12.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-reader-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/sjson-new-scalajson_2.12-0.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/sjson-new-murmurhash_2.12-0.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/ssl-config-core_2.12-0.6.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/completion_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scala-library-2.12.20.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scala-compiler-2.12.20.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-relation_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scala-parser-combinators_2.12-1.1.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zero-allocation-hashing-0.16.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/command_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-style-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/reactive-streams-1.0.3.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-position_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/ivy-2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/disruptor-3.4.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scala-reflect-2.12.20.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-apiinfo_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scripted-plugin_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/test-agent-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/protocol_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/template-resolver-0.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/util-cache_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-compile-core_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/sbt-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-native-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-persist-core-assembly-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jna-platform-5.12.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/testing_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/ipcsocket-1.6.3.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/actions_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/scala-xml_2.12-2.3.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-lm-integration_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jline-builtins-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/shaded-jawn-parser_2.12-1.3.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/tasks_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/task-system_2.12-1.10.11.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/compiler-bridge_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/shaded-scalajson_2.12-1.0.0-M4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/jansi-2.4.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/zinc-persist_2.12-1.10.8.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.11/shaded-apache-httpasyncclient-0.7.0.jar
