/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/sbt-bloop_2.12_1.0/2.0.10/sbt-bloop_2.12_1.0-2.0.10.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/bloop-config_2.12/2.3.2/bloop-config_2.12-2.3.2.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/plokhotnyuk/jsoniter-scala/jsoniter-scala-core_2.12/2.13.5.2/jsoniter-scala-core_2.12-2.13.5.2.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/unroll-annotation_2.12/0.1.12/unroll-annotation_2.12-0.1.12.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-xml_2.12-2.3.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-terminal-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/io_2.12-1.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/main-settings_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-persist_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-terminal-jni-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/slf4j-api-1.7.36.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-control_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-2.14.7-sbt-9c3b6aca11c57e339441442bbf58e550cdfecb79.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/compiler-bridge_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/command_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/log4j-api-2.17.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/sjson-new-core_2.12-0.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/sbinary_2.12-0.5.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/error_prone_annotations-2.4.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/gigahorse-core_2.12-0.7.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-persist-core-assembly-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/config-1.4.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/test-interface-1.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/main_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-cache_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/librarymanagement-core_2.12-1.10.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-compile-core_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/launcher-interface-1.4.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-lm-integration_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/gigahorse-apache-http_2.12-0.7.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/task-system_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/checker-qual-3.4.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/collections_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-apiinfo_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/caffeine-2.8.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/tasks_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jna-5.12.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/test-agent-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/log4j-core-2.17.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/testing_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scala-collection-compat_2.12-2.12.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/file-tree-views-2.1.12.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-tracking_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/librarymanagement-ivy_2.12-1.10.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-reader-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/sjson-new-scalajson_2.12-0.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/sjson-new-murmurhash_2.12-0.10.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/ssl-config-core_2.12-0.6.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/sbt-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scala-library-2.12.20.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/actions_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scala-compiler-2.12.20.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/protocol_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scala-parser-combinators_2.12-1.1.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zero-allocation-hashing-0.16.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-style-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scripted-plugin_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/reactive-streams-1.0.3.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-relation_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/disruptor-3.4.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-core_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scala-reflect-2.12.20.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/template-resolver-0.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/run_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-native-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/core-macros_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/logic_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-classfile_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-logging_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jna-platform-5.12.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-interface-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/ipcsocket-1.6.3.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/util-position_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-classpath_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/scala-xml_2.12-2.3.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/compiler-interface-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jline-builtins-3.27.1.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/shaded-jawn-parser_2.12-1.3.2.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/lm-coursier-shaded_2.12-2.1.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/zinc-compile_2.12-1.10.4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jsch-0.2.17.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/shaded-scalajson_2.12-1.0.0-M4.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/jansi-2.4.0.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/completion_2.12-1.10.5.jar:/Users/<USER>/.sbt/boot/scala-2.12.20/org.scala-sbt/sbt/1.10.5/shaded-apache-httpasyncclient-0.7.0.jar
