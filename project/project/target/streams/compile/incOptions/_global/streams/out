[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /Users/<USER>/SourceCode/cats-state/project/project/target/scala-2.12/sbt-1.0/classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /Users/<USER>/SourceCode/cats-state/project/project/target/scala-2.12/sbt-1.0/classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: /Users/<USER>/SourceCode/cats-state/project/project/target/scala-2.12/sbt-1.0/classes.bak[0m
