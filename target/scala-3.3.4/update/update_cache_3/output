{"cachedDescriptor": ".", "configurations": [{"configuration": {"name": "compile"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-core_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-generic_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-generic_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-parser_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-std_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-std_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-numbers_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-numbers_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-jawn_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-jawn_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "jawn-parser_3", "revision": "1.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jawn-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/jawn", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "http://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "compile-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-core_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-generic_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-generic_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-parser_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-std_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-std_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-numbers_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-numbers_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-jawn_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-jawn_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "jawn-parser_3", "revision": "1.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jawn-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/jawn", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "http://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "docs"}, "modules": [], "details": []}, {"configuration": {"name": "optional"}, "modules": [], "details": []}, {"configuration": {"name": "plugin"}, "modules": [], "details": []}, {"configuration": {"name": "pom"}, "modules": [], "details": []}, {"configuration": {"name": "provided"}, "modules": [], "details": []}, {"configuration": {"name": "runtime"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-core_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-generic_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-generic_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-parser_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-std_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-std_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-numbers_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-numbers_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-jawn_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-jawn_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "jawn-parser_3", "revision": "1.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jawn-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/jawn", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "http://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-core_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-generic_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-generic_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-parser_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-std_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-std_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-numbers_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-numbers_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-jawn_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-jawn_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "jawn-parser_3", "revision": "1.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jawn-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/jawn", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "http://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "scala-doc-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scaladoc_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scaladoc_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scaladoc_3/3.3.4/scaladoc_3-3.3.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scaladoc_3/3.3.4/scaladoc_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala3-compiler_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-compiler_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-compiler_3/3.3.4/scala3-compiler_3-3.3.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-compiler_3/3.3.4/scala3-compiler_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala3-tasty-inspector_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-tasty-inspector_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-tasty-inspector_3/3.3.4/scala3-tasty-inspector_3-3.3.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-tasty-inspector_3/3.3.4/scala3-tasty-inspector_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark/0.62.2/flexmark-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark/0.62.2/flexmark-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-ast", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-ast", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-ast/0.62.2/flexmark-util-ast-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-ast/0.62.2/flexmark-util-ast-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-data", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-data", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-data/0.62.2/flexmark-util-data-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-data/0.62.2/flexmark-util-data-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-html", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-html", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-html/0.62.2/flexmark-util-html-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-html/0.62.2/flexmark-util-html-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-anchorlink", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-anchorlink", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-anchorlink/0.62.2/flexmark-ext-anchorlink-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-anchorlink/0.62.2/flexmark-ext-anchorlink-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-autolink", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-autolink", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-autolink/0.62.2/flexmark-ext-autolink-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-autolink/0.62.2/flexmark-ext-autolink-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-emoji", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-emoji", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-emoji/0.62.2/flexmark-ext-emoji-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-emoji/0.62.2/flexmark-ext-emoji-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-gfm-strikethrough", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-gfm-strikethrough", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-gfm-strikethrough/0.62.2/flexmark-ext-gfm-strikethrough-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-gfm-strikethrough/0.62.2/flexmark-ext-gfm-strikethrough-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-gfm-tasklist", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-gfm-tasklist", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-gfm-tasklist/0.62.2/flexmark-ext-gfm-tasklist-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-gfm-tasklist/0.62.2/flexmark-ext-gfm-tasklist-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-wikilink", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-wikilink", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-wikilink/0.62.2/flexmark-ext-wikilink-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-wikilink/0.62.2/flexmark-ext-wikilink-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-tables", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-tables", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-tables/0.62.2/flexmark-ext-tables-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-tables/0.62.2/flexmark-ext-tables-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-yaml-front-matter", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-yaml-front-matter", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-yaml-front-matter/0.62.2/flexmark-ext-yaml-front-matter-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-yaml-front-matter/0.62.2/flexmark-ext-yaml-front-matter-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "nl.big-o", "name": "liqp", "revision": "0.8.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "liqp", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/nl/big-o/liqp/0.8.2/liqp-0.8.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/nl/big-o/liqp/0.8.2/liqp-0.8.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/bkiers/Liqp", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "http://www.opensource.org/licenses/mit-license.php"]], "callers": []}, {"module": {"organization": "org.jsoup", "name": "jsoup", "revision": "1.17.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsoup", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jsoup/jsoup/1.17.2/jsoup-1.17.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jsoup/jsoup/1.17.2/jsoup-1.17.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://jsoup.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The MIT License", "https://jsoup.org/license"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-yaml", "revision": "2.15.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-yaml", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.1/jackson-dataformat-yaml-2.15.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.1/jackson-dataformat-yaml-2.15.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-text", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala3-interfaces", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-interfaces", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-interfaces/3.3.4/scala3-interfaces-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-interfaces/3.3.4/scala3-interfaces-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "tasty-core_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "tasty-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/tasty-core_3/3.3.4/tasty-core_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/tasty-core_3/3.3.4/tasty-core_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-asm", "revision": "9.6.0-scala-1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-asm", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-asm/9.6.0-scala-1/scala-asm-9.6.0-scala-1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-asm/9.6.0-scala-1/scala-asm-9.6.0-scala-1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3-clause", "http://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-interface", "revision": "1.9.6", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.6/compiler-interface-1.9.6.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.6/compiler-interface-1.9.6.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-reader", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-reader", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-reader/3.25.1/jline-reader-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.25.1/jline-reader-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal/3.25.1/jline-terminal-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.25.1/jline-terminal-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jna", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jna", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.25.1/jline-terminal-jna-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.25.1/jline-terminal-jna-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-builder", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-builder", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-builder/0.62.2/flexmark-util-builder-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-builder/0.62.2/flexmark-util-builder-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-collection", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-collection", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-collection/0.62.2/flexmark-util-collection-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-collection/0.62.2/flexmark-util-collection-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-dependency", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-dependency", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-dependency/0.62.2/flexmark-util-dependency-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-dependency/0.62.2/flexmark-util-dependency-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-format", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-format", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-format/0.62.2/flexmark-util-format-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-format/0.62.2/flexmark-util-format-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-misc", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-misc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-misc/0.62.2/flexmark-util-misc-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-misc/0.62.2/flexmark-util-misc-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-sequence", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-sequence", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-sequence/0.62.2/flexmark-util-sequence-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-sequence/0.62.2/flexmark-util-sequence-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-visitor", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-visitor", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-visitor/0.62.2/flexmark-util-visitor-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-visitor/0.62.2/flexmark-util-visitor-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "org.jetbrains", "name": "annotations", "revision": "15.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jetbrains/annotations/15.0/annotations-15.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jetbrains/annotations/15.0/annotations-15.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.jetbrains.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util/0.62.2/flexmark-util-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util/0.62.2/flexmark-util-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "org.nibor.autolink", "name": "autolink", "revision": "0.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "autolink", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/nibor/autolink/autolink/0.6.0/autolink-0.6.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/nibor/autolink/autolink/0.6.0/autolink-0.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/robinst/autolink-java", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "http://www.opensource.org/licenses/mit-license.php"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-jira-converter", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-jira-converter", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-jira-converter/0.62.2/flexmark-jira-converter-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-jira-converter/0.62.2/flexmark-jira-converter-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "org.antlr", "name": "antlr4-runtime", "revision": "4.7.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "antlr4-runtime", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/antlr/antlr4-runtime/4.7.2/antlr4-runtime-4.7.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/antlr/antlr4-runtime/4.7.2/antlr4-runtime-4.7.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "http://www.antlr.org/license.html"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.15.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.1/jackson-annotations-2.15.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.15.1/jackson-annotations-2.15.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.15.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.1/jackson-core-2.15.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.1/jackson-core-2.15.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.15.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.1/jackson-databind-2.15.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.1/jackson-databind-2.15.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.12.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.12.1/jackson-datatype-jsr310-2.12.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.12.1/jackson-datatype-jsr310-2.12.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "ua.co.k", "name": "strftime4j", "revision": "1.0.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "strftime4j", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/ua/co/k/strftime4j/1.0.5/strftime4j-1.0.5.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ua/co/k/strftime4j/1.0.5/strftime4j-1.0.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/msangel/strftime4j", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT License", "http://www.opensource.org/licenses/mit-license.php"]], "callers": []}, {"module": {"organization": "org.yaml", "name": "snake<PERSON>l", "revision": "2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "snake<PERSON>l", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/yaml/snakeyaml/2.0/snakeyaml-2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/yaml/snakeyaml/2.0/snakeyaml-2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://bitbucket.org/snakeyaml/snakeyaml", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-interface", "revision": "1.9.8", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.8/util-interface-1.9.8.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.8/util-interface-1.9.8.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-native", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-native", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-native/3.25.1/jline-native-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.25.1/jline-native-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna", "revision": "5.14.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-util-options", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-util-options", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-options/0.62.2/flexmark-util-options-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-util-options/0.62.2/flexmark-util-options-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-ins", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-ins", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-ins/0.62.2/flexmark-ext-ins-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-ins/0.62.2/flexmark-ext-ins-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}, {"module": {"organization": "com.vladsch.flexmark", "name": "flexmark-ext-superscript", "revision": "0.62.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "flexmark-ext-superscript", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-superscript/0.62.2/flexmark-ext-superscript-0.62.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/vladsch/flexmark/flexmark-ext-superscript/0.62.2/flexmark-ext-superscript-0.62.2.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 2-Clause License", "http://opensource.org/licenses/BSD-2-Clause"]], "callers": []}], "details": []}, {"configuration": {"name": "scala-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-compiler_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-compiler_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-compiler_3/3.3.4/scala3-compiler_3-3.3.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-compiler_3/3.3.4/scala3-compiler_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala3-interfaces", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-interfaces", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-interfaces/3.3.4/scala3-interfaces-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-interfaces/3.3.4/scala3-interfaces-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "tasty-core_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "tasty-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/tasty-core_3/3.3.4/tasty-core_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/tasty-core_3/3.3.4/tasty-core_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-asm", "revision": "9.6.0-scala-1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-asm", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-asm/9.6.0-scala-1/scala-asm-9.6.0-scala-1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-asm/9.6.0-scala-1/scala-asm-9.6.0-scala-1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3-clause", "http://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "compiler-interface", "revision": "1.9.6", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "compiler-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.6/compiler-interface-1.9.6.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.9.6/compiler-interface-1.9.6.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/zinc", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-reader", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-reader", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-reader/3.25.1/jline-reader-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.25.1/jline-reader-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal/3.25.1/jline-terminal-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.25.1/jline-terminal-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-terminal-jna", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-terminal-jna", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.25.1/jline-terminal-jna-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jna/3.25.1/jline-terminal-jna-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "util-interface", "revision": "1.9.8", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "util-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.8/util-interface-1.9.8.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.9.8/util-interface-1.9.8.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/sbt/sbt", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://github.com/sbt/sbt/blob/develop/LICENSE"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline-native", "revision": "3.25.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline-native", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline-native/3.25.1/jline-native-3.25.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.25.1/jline-native-3.25.1.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna", "revision": "5.14.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "sources"}, "modules": [], "details": []}, {"configuration": {"name": "test"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-core_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-generic_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-generic_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-parser_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatestplus", "name": "scalacheck-1-17_3", "revision": "3.2.18.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalacheck-1-17_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatestplus/scalacheck-1-17_3/3.2.18.0/scalacheck-1-17_3-3.2.18.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatestplus/scalacheck-1-17_3/3.2.18.0/scalacheck-1-17_3-3.2.18.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalatest/scalatestplus-scalacheck", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-std_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-std_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-numbers_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-numbers_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-jawn_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-jawn_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-core_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-core_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-featurespec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-featurespec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-flatspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-flatspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-freespec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-freespec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funsuite_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funsuite_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-propspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-propspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-refspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-refspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-wordspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-wordspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-diagrams_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-diagrams_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-matchers-core_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-matchers-core_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-shouldmatchers_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-shouldmatchers_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-mustmatchers_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-mustmatchers_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalacheck", "name": "scalacheck_3", "revision": "1.17.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.scalacheck/scalacheck_3/1.17.0/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalacheck_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalacheck/scalacheck_3/1.17.0/scalacheck_3-1.17.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.scalacheck/scalacheck_3/1.17.0/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalacheck/scalacheck_3/1.17.0/scalacheck_3-1.17.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalacheck.org", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.scalacheck/scalacheck_3/1.17.0/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3-clause", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "jawn-parser_3", "revision": "1.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jawn-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/jawn", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "http://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.scalactic", "name": "scalactic_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalactic_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-compatible", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-compatible", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_3", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}], "details": []}, {"configuration": {"name": "test-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala3-library_3", "revision": "3.3.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala3-library_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar", "extraAttributes": {"info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scala/scala3", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-core_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-core_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-core_3/0.14.10/circe-core_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-core_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-generic_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-generic_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-generic_3/0.14.10/circe-generic_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-generic_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-parser_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-parser_3/0.14.10/circe-parser_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-parser_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatestplus", "name": "scalacheck-1-17_3", "revision": "3.2.18.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalacheck-1-17_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatestplus/scalacheck-1-17_3/3.2.18.0/scalacheck-1-17_3-3.2.18.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatestplus/scalacheck-1-17_3/3.2.18.0/scalacheck-1-17_3-3.2.18.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalatest/scalatestplus-scalacheck", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.14/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_3", "revision": "2.12.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_3/2.12.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.12.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-kernel_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-kernel_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-effect-std_3", "revision": "3.5.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-effect-std_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/cats-effect", "extraAttributes": {"info.apiURL": "https://typelevel.org/cats-effect/api/3.x/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-numbers_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-numbers_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-numbers_3/0.14.10/circe-numbers_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-numbers_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "io.circe", "name": "circe-jawn_3", "revision": "0.14.10", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "circe-jawn_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/circe/circe-jawn_3/0.14.10/circe-jawn_3-0.14.10.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/circe/circe", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/io.circe/circe-jawn_3/0.14.10/", "info.releaseNotesUrl": "https://github.com/circe/circe/releases/tag/v0.14.10", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-core_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-core_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-featurespec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-featurespec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-flatspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-flatspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-freespec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-freespec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funsuite_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funsuite_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-propspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-propspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-refspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-refspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-wordspec_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-wordspec_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-diagrams_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-diagrams_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-matchers-core_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-matchers-core_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-shouldmatchers_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-shouldmatchers_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-mustmatchers_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-mustmatchers_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalacheck", "name": "scalacheck_3", "revision": "1.17.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.scalacheck/scalacheck_3/1.17.0/", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalacheck_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalacheck/scalacheck_3/1.17.0/scalacheck_3-1.17.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.scalacheck/scalacheck_3/1.17.0/", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalacheck/scalacheck_3/1.17.0/scalacheck_3-1.17.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalacheck.org", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.scalacheck/scalacheck_3/1.17.0/", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD 3-clause", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "jawn-parser_3", "revision": "1.6.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jawn-parser_3", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/jawn-parser_3/1.6.0/jawn-parser_3-1.6.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/typelevel/jawn", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/jawn-parser_3/1.6.0/", "info.releaseNotesUrl": "https://github.com/typelevel/jawn/releases/tag/v1.6.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "http://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.scalactic", "name": "scalactic_3", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalactic_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-compatible", "revision": "3.2.19", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-compatible", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_3", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_3", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}], "details": []}], "stats": {"resolveTime": -1, "downloadTime": -1, "downloadSize": -1, "cached": false}, "stamps": {}}