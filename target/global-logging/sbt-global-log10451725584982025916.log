[0m[[0m[0mdebug[0m] [0m[0m> Exec(compile, Some(1da1fe95-f3ff-483c-a885-682adc2a9fc2), Some(CommandSource(console0)))[0m
[0m[[0m[0mdebug[0m] [0m[0mEvaluating tasks: Compile / compile[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning task... Cancel: Signal, check cycles: false, forcegc: true[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 2 Scala sources to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes ...[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E008] Not Found Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/Main.scala:52:29 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m52 |[0m  [33mval[0m [36mcombinedEvent[0m = event1 |+| event2[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                      ^^^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m                      value |+| is not a member of Event[0m
[0m[[0m[31merror[0m] [0m[0mone error found[0m
[0m[[0m[0mdebug[0m] [0m[0mForcing garbage collection...[0m
[0m[[0m[31merror[0m] [0m[0m(Compile / [31mcompileIncremental[0m) Compilation failed[0m
[0m[[0m[31merror[0m] [0m[0mTotal time: 1 s, completed 26 Jun 2025, 15:17:51[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(shell, None, None)[0m
