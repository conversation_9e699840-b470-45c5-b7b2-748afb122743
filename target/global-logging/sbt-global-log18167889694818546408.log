[0m[[0m[0mdebug[0m] [0m[0m> Exec(test, Some(e08c6764-485e-4e05-b93f-9652ac4e2c37), Some(CommandSource(console0)))[0m
[0m[[0m[0mdebug[0m] [0m[0mEvaluating tasks: Test / test[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning task... Cancel: Signal, check cycles: false, forcegc: true[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 2 Scala sources to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes ...[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes ...[0m
