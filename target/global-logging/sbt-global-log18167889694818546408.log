[0m[[0m[0mdebug[0m] [0m[0m> Exec(test, Some(e08c6764-485e-4e05-b93f-9652ac4e2c37), Some(CommandSource(console0)))[0m
[0m[[0m[0mdebug[0m] [0m[0mEvaluating tasks: Test / test[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning task... Cancel: Signal, check cycles: false, forcegc: true[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 2 Scala sources to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes ...[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes ...[0m
[0m[[0m[0minfo[0m] [0m[0m[32mMainSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mStack operations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work correctly with State monad[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Option[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Either[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mMonoidSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mEvent Monoid[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should have Start as empty element[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine with Start as identity[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine Foo events by concatenating strings[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine Bar events by adding integers[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should give precedence to Foo when combining Foo and Bar[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be associative[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mResponse Monoid[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should have empty Response with Start events[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine Responses by combining their fields[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with empty as identity[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be associative[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with combineAll for multiple responses[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mRun completed in 89 milliseconds.[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTotal number of tests run: 14[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mSuites: completed 2, aborted 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTests: succeeded 14, failed 0, canceled 0, ignored 0, pending 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mAll tests passed.[0m[0m
[0m[[0m[32msuccess[0m] [0m[0mTotal time: 1 s, completed 26 Jun 2025, 15:08:10[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(shell, None, None)[0m
