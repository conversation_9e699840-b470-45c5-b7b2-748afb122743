[0m[[0m[0mdebug[0m] [0m[0m> Exec(compile, Some(3b52619b-c138-442e-ac8c-b2e90d778954), Some(CommandSource(console0)))[0m
[0m[[0m[0mdebug[0m] [0m[0mEvaluating tasks: Compile / compile[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning task... Cancel: Signal, check cycles: false, forcegc: true[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 2 Scala sources to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mForcing garbage collection...[0m
[0m[[0m[32msuccess[0m] [0m[0mTotal time: 0 s, completed 26 Jun 2025, 17:59:12[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(shell, None, None)[0m
