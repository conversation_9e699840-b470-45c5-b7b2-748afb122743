[0m[[0m[0mdebug[0m] [0m[0m> Exec(test, Some(c7c60191-f89f-4f7c-85e7-794bf54fb83c), Some(CommandSource(console0)))[0m
[0m[[0m[0mdebug[0m] [0m[0mEvaluating tasks: Test / test[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning task... Cancel: Signal, check cycles: false, forcegc: true[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes ...[0m
[0m[[0m[0minfo[0m] [0m[0m[32mMainSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mStack operations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work correctly with State monad[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Option[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Either[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mRun completed in 197 milliseconds.[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTotal number of tests run: 3[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mSuites: completed 1, aborted 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTests: succeeded 3, failed 0, canceled 0, ignored 0, pending 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mAll tests passed.[0m[0m
[0m[[0m[32msuccess[0m] [0m[0mTotal time: 1 s, completed 26 Jun 2025, 14:53:38[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(shell, None, None)[0m
