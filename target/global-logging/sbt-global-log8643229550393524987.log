[0m[[0m[0mdebug[0m] [0m[0m> Exec(__stopWatch network-1, None, None)[0m
[0m[[0m[33mwarn[0m] [0m[0mbuild source files have changed[0m
[0m[[0m[33mwarn[0m] [0m[0mmodified files: [0m
[0m[[0m[33mwarn[0m] [0m[0m  /Users/<USER>/SourceCode/cats-state/build.sbt[0m
[0m[[0m[33mwarn[0m] [0m[0mApply these changes by running `reload`.[0m
[0m[[0m[33mwarn[0m] [0m[0mAutomatically reload the build when source changes are detected by setting `Global / onChangedBuildSource := ReloadOnSourceChanges`.[0m
[0m[[0m[33mwarn[0m] [0m[0mDisable this warning by setting `Global / onChangedBuildSource := IgnoreSourceChanges`.[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(shell, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0mjsonRpcNotify: JsonRpcNotificationMessage(2.0, shutdown, [false,null])[0m
[0m[[0m[0mdebug[0m] [0m[0mjsonRpcNotify: JsonRpcNotificationMessage(2.0, shutdown, [true,null])[0m
