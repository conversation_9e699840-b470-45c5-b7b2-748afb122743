[0m[[0m[0mdebug[0m] [0m[0m> Exec(sbt test, Some(8b2e08c4-766a-4811-8593-5d27563e6a25), Some(CommandSource(console0)))[0m
[0m[[0m[31merror[0m] [0m[0mExpected ID character[0m
[0m[[0m[31merror[0m] [0m[0mNot a valid command: sbt (similar: set, boot, last)[0m
[0m[[0m[31merror[0m] [0m[0mExpected 'sbtRebootNetwork'[0m
[0m[[0m[31merror[0m] [0m[0mExpected project ID[0m
[0m[[0m[31merror[0m] [0m[0mExpected configuration[0m
[0m[[0m[31merror[0m] [0m[0mExpected ':'[0m
[0m[[0m[31merror[0m] [0m[0mExpected key[0m
[0m[[0m[31merror[0m] [0m[0mNot a valid key: sbt (similar: test, state, ivySbt)[0m
[0m[[0m[31merror[0m] [0m[0mExpected 'sbtPromptChannel'[0m
[0m[[0m[31merror[0m] [0m[0mExpected 'sbtRebootImpl'[0m
[0m[[0m[31merror[0m] [0m[0mExpected 'sbtReportResult'[0m
[0m[[0m[31merror[0m] [0m[0mExpected 'sbtMapExec'[0m
[0m[[0m[31merror[0m] [0m[0mExpected 'sbtCompleteExec'[0m
[0m[[0m[31merror[0m] [0m[0msbt test[0m
[0m[[0m[31merror[0m] [0m[0m   ^[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(shell, None, None)[0m
