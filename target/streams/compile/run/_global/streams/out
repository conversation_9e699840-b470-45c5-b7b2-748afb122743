[0m[[0m[31merror[0m] [0m[0mjava.lang.RuntimeException: Invalid event Foo(user-data) for current state Start[0m
[0m[[0m[31merror[0m] [0m[0m	at FSM.run(model.scala:62)[0m
[0m[[0m[31merror[0m] [0m[0m	at Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1$$anonfun$1$$anonfun$1$$anonfun$1(Main.scala:64)[0m
[0m[[0m[31merror[0m] [0m[0m	at flatMap @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1$$anonfun$1$$anonfun$1$$anonfun$1(Main.scala:64)[0m
[0m[[0m[31merror[0m] [0m[0m	at println @ Main$.run(Main.scala:52)[0m
[0m[[0m[31merror[0m] [0m[0m	at flatMap @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1$$anonfun$1$$anonfun$1(Main.scala:62)[0m
[0m[[0m[31merror[0m] [0m[0m	at get @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1$$anonfun$1(Main.scala:61)[0m
[0m[[0m[31merror[0m] [0m[0m	at get @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1$$anonfun$1(Main.scala:61)[0m
[0m[[0m[31merror[0m] [0m[0m	at flatMap @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1$$anonfun$1(Main.scala:61)[0m
[0m[[0m[31merror[0m] [0m[0m	at set @ FSM.run$$anonfun$1(model.scala:42)[0m
[0m[[0m[31merror[0m] [0m[0m	at map @ FSM.run$$anonfun$1(model.scala:42)[0m
[0m[[0m[31merror[0m] [0m[0m	at map @ FSM.run$$anonfun$1(model.scala:42)[0m
[0m[[0m[31merror[0m] [0m[0m	at println @ Main$.run(Main.scala:52)[0m
[0m[[0m[31merror[0m] [0m[0m	at flatMap @ FSM.run(model.scala:41)[0m
[0m[[0m[31merror[0m] [0m[0m	at flatMap @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2$$anonfun$1(Main.scala:60)[0m
[0m[[0m[31merror[0m] [0m[0m	at println @ Main$.run(Main.scala:52)[0m
[0m[[0m[31merror[0m] [0m[0m	at flatMap @ Main$.run$$anonfun$1$$anonfun$1$$anonfun$2(Main.scala:58)[0m
[0m[[0m[31merror[0m] [0m[0m	at ofSingleImmutableMap @ Main$.run$$anonfun$1$$anonfun$1(Main.scala:56)[0m
[0m[[0m[31merror[0m] [0m[0m(Compile / [31mrun[0m) Invalid event Foo(user-data) for current state Start[0m
