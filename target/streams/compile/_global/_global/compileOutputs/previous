["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiOneResult$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiOneResult.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiTwoResult$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiTwoResult.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Bar$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Bar.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Begin$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Begin.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ConcreteInterpreter$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ConcreteInterpreter.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Done$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Done.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/FSM$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/FSM.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Foo$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Foo.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Interpreter.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$$anon$1.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$Accumulator$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$Accumulator.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Result.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State$$anon$1.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/zinc/inc_compile_3.zip"]]