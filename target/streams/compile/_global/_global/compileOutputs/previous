["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event$$anon$1.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event$Bar$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event$Bar.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event$Foo$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event$Foo.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package$given_Monoid_Event$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package$given_Monoid_Response$.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package.class", "/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/zinc/inc_compile_3.zip"]]