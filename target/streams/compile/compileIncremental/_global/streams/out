[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 2 products, 1 sources, 2 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/src/main/scala/model.scala, ${BASE}/src/main/scala/Main.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/src/main/scala/model.scala), removed = Set(), changed = Set(${BASE}/src/main/scala/Main.scala), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial source changes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	removed: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	added: Set(${BASE}/src/main/scala/model.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	modified: Set(${BASE}/src/main/scala/Main.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidated products: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mExternal API changes: API Changes: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mModified binary dependencies: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial directly invalidated classes: Set(Main)[0m
[0m[[0m[0mdebug[0m] [0m[0mSources indirectly invalidated by:[0m
[0m[[0m[0mdebug[0m] [0m[0m	product: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	binary dep: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	external source: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mall 2 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: Main[0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 2 Scala sources to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-sbt-bridge/3.3.4/scala3-sbt-bridge-3.3.4.jar.[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] Running cached compiler 62ebbc5 for Scala Compiler version 3.3.4[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0mScala compilation took 0.460100542 s[0m
[0m[[0m[0mdebug[0m] [0m[0mdone compiling[0m
