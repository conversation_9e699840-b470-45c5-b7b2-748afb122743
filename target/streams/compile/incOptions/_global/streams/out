[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Event.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Event.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Event.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Event.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mRegistering generated classes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiSuccess.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	EntityAggregator$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	EntityAggregator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiSuccess$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	EntityAggregator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiFailure$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiFailure.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiSuccess.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	EntityAggregator$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	EntityAggregator$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiSuccess$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	EntityAggregator.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiFailure$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiFailure.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes.bak[0m
