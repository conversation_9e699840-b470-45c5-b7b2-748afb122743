[0m[[0m[0mdebug[0m] [0m[0mPackaging /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/cats-state_3-0.1.0-SNAPSHOT.jar ...[0m
[0m[[0m[0mdebug[0m] [0m[0mInput file mappings:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Begin$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Bar.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Event.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$Accumulator.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$Accumulator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$Accumulator$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Result.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Done$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Bar$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Result.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Result.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ConcreteInterpreter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Event.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Event.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	model$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/model$package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Bar.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Bar.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	State.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/State.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Begin.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Interpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiTwoResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiOneResult.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Foo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ConcreteInterpreter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/FSM.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Done.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/FSM$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Main.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Main.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiOneResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ConcreteInterpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ConcreteInterpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Foo.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiOneResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiOneResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Done.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Done.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	FSM.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/FSM.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Foo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Foo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiTwoResult$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ApiTwoResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/ApiTwoResult.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Interpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Interpreter.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	Begin.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Begin.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	Response.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	  /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes/Response.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mDone packaging.[0m
