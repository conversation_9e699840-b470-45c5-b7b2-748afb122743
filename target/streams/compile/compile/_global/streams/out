[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E008] Not Found Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/Main.scala:5:10 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m5 |[0m[33mimport[0m io.circe.Encoder[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m  |[0m       ^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m  |[0m       value circe is not a member of io[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E008] Not Found Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/Main.scala:6:10 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m6 |[0m[33mimport[0m io.circe.syntax.*[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m  |[0m       ^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m  |[0m       value circe is not a member of io[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/Main.scala:10:81 -[0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m10 |[0m  [33mcase class[0m [35mAccumulator[0m([36mapiOne[0m: [35mOption[0m[[35mResult[0m], [36mapiTwo[0m: [35mOption[0m[[35mResult[0m]) derives [35mEncoder[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                                                                                 ^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m     type Object in derives clause of Accumulator has no type parameters[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E172] Type Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/Main.scala:34:37 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m34 |[0m      IO.println(acc.asJson.noSpaces)[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                                     ^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0mAmbiguous given instances: both [33mvalue[0m [35mcatsStdShowForByte[0m in [33mtrait[0m [35mByteInstances[0m and [33mvalue[0m [35mcatsStdShowForLong[0m in [33mtrait[0m [35mLongInstances[0m match type cats.Show[Any] of parameter S of [33mmethod[0m [35mprintln[0m in [33mobject[0m [35mIO[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E008] Not Found Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/model.scala:4:10 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m4 |[0m[33mimport[0m io.circe.Encoder[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m  |[0m       ^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m  |[0m       value circe is not a member of io[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E008] Not Found Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/model.scala:5:10 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m5 |[0m[33mimport[0m io.circe.Json[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m  |[0m       ^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m  |[0m       value circe is not a member of io[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/model.scala:16:58 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m16 |[0m[33mcase class[0m [35mApiOneResult[0m([36mx[0m: [35mString[0m) [33mextends[0m [35mResult[0m derives [35mEncoder[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                                                          ^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m    type Object in derives clause of ApiOneResult has no type parameters[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/model.scala:17:55 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m17 |[0m[33mcase class[0m [35mApiTwoResult[0m([36my[0m: [35mInt[0m) [33mextends[0m [35mResult[0m derives [35mEncoder[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                                                       ^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m    type Object in derives clause of ApiTwoResult has no type parameters[0m
[0m[[0m[31merror[0m] [0m[0m8 errors found[0m
