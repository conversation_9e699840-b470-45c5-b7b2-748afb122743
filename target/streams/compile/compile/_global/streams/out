[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E007] Type Mismatch Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/model.scala:45:21 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m45 |[0m          } [33myield[0m run(Foo([31m"user-data"[0m))[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                  ^^^^^^^^^^^^^^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0mFound:    cats.effect.IO[Store][0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0mRequired: cats.effect.std.MapRef[cats.effect.IO, String, Option[Result]][0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m longer explanation available when compiling with `-explain`[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m-- [E007] Type Mismatch Error: /Users/<USER>/SourceCode/cats-state/src/main/scala/model.scala:52:21 [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m52 |[0m          } [33myield[0m run(Done)[0m
[0m[[0m[31merror[0m] [0m[0m[31m[31m   |[0m                  ^^^^^^^^^[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0mFound:    cats.effect.IO[Store][0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0mRequired: cats.effect.std.MapRef[cats.effect.IO, String, Option[Result]][0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m[0m
[0m[[0m[31merror[0m] [0m[0m[31m   |[0m longer explanation available when compiling with `-explain`[0m
[0m[[0m[31merror[0m] [0m[0mtwo errors found[0m
