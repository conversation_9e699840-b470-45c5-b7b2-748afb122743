{"{\"organization\":\"org.typelevel\",\"name\":\"cats-core\",\"revision\":\"2.12.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/Users/<USER>/SourceCode/cats-state/build.sbt", "startLine": 8}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"cats-effect\",\"revision\":\"3.5.7\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/Users/<USER>/SourceCode/cats-state/build.sbt", "startLine": 8}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala3-library\",\"revision\":\"3.3.4\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/Users/<USER>/SourceCode/cats-state/build.sbt", "startLine": 8}, "type": "LinePosition"}, "{\"organization\":\"org.scalatest\",\"name\":\"scalatest\",\"revision\":\"3.2.19\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/Users/<USER>/SourceCode/cats-state/build.sbt", "startLine": 8}, "type": "LinePosition"}, "{\"organization\":\"org.scalatestplus\",\"name\":\"scalacheck-1-17\",\"revision\":\"3.2.18.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/Users/<USER>/SourceCode/cats-state/build.sbt", "startLine": 8}, "type": "LinePosition"}}