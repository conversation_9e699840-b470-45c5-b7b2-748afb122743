{"cachedDescriptor": ".", "configurations": [{"configuration": {"name": "compile"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "compile-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "docs"}, "modules": [], "details": []}, {"configuration": {"name": "optional"}, "modules": [], "details": []}, {"configuration": {"name": "plugin"}, "modules": [], "details": []}, {"configuration": {"name": "pom"}, "modules": [], "details": []}, {"configuration": {"name": "provided"}, "modules": [], "details": []}, {"configuration": {"name": "runtime"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}], "details": []}, {"configuration": {"name": "scala-doc-tool"}, "modules": [], "details": []}, {"configuration": {"name": "scala-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-compiler/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.12/scala-compiler-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-compiler/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.12/scala-compiler-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-compiler/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.13.12", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-compiler/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.12/scala-compiler-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-compiler/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.12/scala-compiler-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-compiler/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.github.java-diff-utils", "name": "java-diff-utils", "revision": "4.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "java-diff-utils", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.12/java-diff-utils-4.12-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.12/java-diff-utils-4.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "org.jline", "name": "jline", "revision": "3.22.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline/3.22.0/jline-3.22.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline/3.22.0/jline-3.22.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [], "callers": []}, {"module": {"organization": "net.java.dev.jna", "name": "jna", "revision": "5.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jna", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/java-native-access/jna", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["LGPL-2.1-or-later", "https://www.gnu.org/licenses/old-licenses/lgpl-2.1"], ["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "sources"}, "modules": [], "details": []}, {"configuration": {"name": "test"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest_2.13/3.2.17/scalatest_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_2.13/3.2.17/scalatest_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-core_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-core_2.13/3.2.17/scalatest-core_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_2.13/3.2.17/scalatest-core_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-featurespec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-featurespec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_2.13/3.2.17/scalatest-featurespec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_2.13/3.2.17/scalatest-featurespec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-flatspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-flatspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_2.13/3.2.17/scalatest-flatspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_2.13/3.2.17/scalatest-flatspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-freespec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-freespec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-freespec_2.13/3.2.17/scalatest-freespec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_2.13/3.2.17/scalatest-freespec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funsuite_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funsuite_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_2.13/3.2.17/scalatest-funsuite_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_2.13/3.2.17/scalatest-funsuite_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funspec_2.13/3.2.17/scalatest-funspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_2.13/3.2.17/scalatest-funspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-propspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-propspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-propspec_2.13/3.2.17/scalatest-propspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_2.13/3.2.17/scalatest-propspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-refspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-refspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-refspec_2.13/3.2.17/scalatest-refspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_2.13/3.2.17/scalatest-refspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-wordspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-wordspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_2.13/3.2.17/scalatest-wordspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_2.13/3.2.17/scalatest-wordspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-diagrams_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-diagrams_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_2.13/3.2.17/scalatest-diagrams_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_2.13/3.2.17/scalatest-diagrams_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-matchers-core_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-matchers-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_2.13/3.2.17/scalatest-matchers-core_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_2.13/3.2.17/scalatest-matchers-core_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-shouldmatchers_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-shouldmatchers_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_2.13/3.2.17/scalatest-shouldmatchers_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_2.13/3.2.17/scalatest-shouldmatchers_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-mustmatchers_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-mustmatchers_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_2.13/3.2.17/scalatest-mustmatchers_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_2.13/3.2.17/scalatest-mustmatchers_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-compatible", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-compatible", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.17/scalatest-compatible-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.17/scalatest-compatible-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalactic", "name": "scalactic_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalactic_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalactic/scalactic_2.13/3.2.17/scalactic_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_2.13/3.2.17/scalactic_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.13", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.13/2.1.0/scala-xml_2.13-2.1.0-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.13/2.1.0/scala-xml_2.13-2.1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}, {"configuration": {"name": "test-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-core_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest_2.13/3.2.17/scalatest_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_2.13/3.2.17/scalatest_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.typelevel", "name": "cats-kernel_2.13", "revision": "2.10.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "cats-kernel_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://typelevel.org/cats", "extraAttributes": {"info.apiURL": "https://www.javadoc.io/doc/org.typelevel/cats-docs_2.13/2.10.0/", "info.releaseNotesUrl": "https://github.com/typelevel/cats/releases/tag/v2.10.0", "info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/licenses/MIT"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-core_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-core_2.13/3.2.17/scalatest-core_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_2.13/3.2.17/scalatest-core_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-featurespec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-featurespec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_2.13/3.2.17/scalatest-featurespec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_2.13/3.2.17/scalatest-featurespec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-flatspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-flatspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_2.13/3.2.17/scalatest-flatspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_2.13/3.2.17/scalatest-flatspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-freespec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-freespec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-freespec_2.13/3.2.17/scalatest-freespec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_2.13/3.2.17/scalatest-freespec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funsuite_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funsuite_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_2.13/3.2.17/scalatest-funsuite_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_2.13/3.2.17/scalatest-funsuite_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-funspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-funspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-funspec_2.13/3.2.17/scalatest-funspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_2.13/3.2.17/scalatest-funspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-propspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-propspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-propspec_2.13/3.2.17/scalatest-propspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_2.13/3.2.17/scalatest-propspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-refspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-refspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-refspec_2.13/3.2.17/scalatest-refspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_2.13/3.2.17/scalatest-refspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-wordspec_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-wordspec_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_2.13/3.2.17/scalatest-wordspec_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_2.13/3.2.17/scalatest-wordspec_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-diagrams_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-diagrams_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_2.13/3.2.17/scalatest-diagrams_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_2.13/3.2.17/scalatest-diagrams_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-matchers-core_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-matchers-core_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_2.13/3.2.17/scalatest-matchers-core_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_2.13/3.2.17/scalatest-matchers-core_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-shouldmatchers_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-shouldmatchers_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_2.13/3.2.17/scalatest-shouldmatchers_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_2.13/3.2.17/scalatest-shouldmatchers_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-mustmatchers_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-mustmatchers_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_2.13/3.2.17/scalatest-mustmatchers_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_2.13/3.2.17/scalatest-mustmatchers_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.12", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12-sources.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.12/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalatest", "name": "scalatest-compatible", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalatest-compatible", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.17/scalatest-compatible-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.17/scalatest-compatible-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalactic", "name": "scalactic_2.13", "revision": "3.2.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scalactic_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalactic/scalactic_2.13/3.2.17/scalactic_2.13-3.2.17-sources.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_2.13/3.2.17/scalactic_2.13-3.2.17-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scalatest.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["the Apache License, ASL Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-xml_2.13", "revision": "2.1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-xml_2.13", "type": "src", "extension": "jar", "classifier": "sources", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.13/2.1.0/scala-xml_2.13-2.1.0-sources.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.13/2.1.0/scala-xml_2.13-2.1.0-sources.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}], "details": []}], "stats": {"resolveTime": -1, "downloadTime": -1, "downloadSize": -1, "cached": false}, "stamps": {}}