[0m[[0m[0mdebug[0m] [0m[0mSubclass fingerprints: List((org.scalacheck.Properties,false,org.scalacheck.ScalaCheckFramework$$anon$5@59dbd053), (org.scalacheck.Prop,false,org.scalacheck.ScalaCheckFramework$$anon$5@bf5bea0), (org.scalacheck.Properties,true,org.scalacheck.ScalaCheckFramework$$anon$5@1b6549a7), (org.scalacheck.Prop,true,org.scalacheck.ScalaCheckFramework$$anon$5@414e7d34), (org.scalatest.Suite,false,org.scalatest.tools.Framework$$anon$1@46107406))[0m
[0m[[0m[0mdebug[0m] [0m[0mAnnotation fingerprints: List((org.scalatest.WrapWith,false,org.scalatest.tools.Framework$$anon$2@512f871a))[0m
