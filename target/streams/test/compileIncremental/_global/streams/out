[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 2 products, 2 sources, 9 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/src/test/scala/ModelSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/src/test/scala/ModelSpec.scala), removed = Set(${BASE}/src/test/scala/MonoidSpec.scala, ${BASE}/src/test/scala/MainSpec.scala), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set(NamesChange(model$package$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(model$package$;init;,[Default]), UsedName(given_Monoid_Event,[Default, Implicit]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(model$package,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(given_Monoid_Response,[Default, Implicit]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event$Bar,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(Event$;Bar;init;,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(copy,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(y,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event$;Bar$;init;,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(model$package$given_Monoid_Response$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(repeatedCombineN,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(cats;kernel;Monoid;init;,[Default]), UsedName(given_Monoid_Response,[Default]), UsedName(reverse,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(combineAll,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(model$package$;given_Monoid_Response$;init;,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(empty,[Default]), UsedName(cats;kernel;Semigroup;init;,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(combine,[Default]), UsedName(intercalate,[Default]), UsedName($isInstanceOf,[Default]), UsedName(combineAllOption,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(isEmpty,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(combineN,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event$Bar$,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(Event$;Bar;init;,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(copy,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(y,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event$;Bar$;init;,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event$Foo,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(Event$;Foo$;init;,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(Event$;Foo;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(Start,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror$;Sum;init;,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(!=,[Default]), UsedName(fromOrdinal,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(Event$;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Response,ModifiedNames(changes = UsedName(bar,[Default]), UsedName(copy$default$2,[Default]), UsedName(copy$default$1,[Default]), UsedName(Response;init;,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(_2,[Default]), UsedName(y,[Default]), UsedName(apply,[Default]), UsedName(_1,[Default]), UsedName(foo,[Default]))), NamesChange(Event$Foo$,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(Event$;Foo$;init;,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(Event$;Foo;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Response$,ModifiedNames(changes = UsedName(bar,[Default]), UsedName(copy$default$2,[Default]), UsedName(copy$default$1,[Default]), UsedName(Response;init;,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(_2,[Default]), UsedName(y,[Default]), UsedName(apply,[Default]), UsedName(_1,[Default]), UsedName(foo,[Default]))), NamesChange(Event$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(Start,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror$;Sum;init;,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(!=,[Default]), UsedName(fromOrdinal,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(Event$;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(model$package$given_Monoid_Event$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(repeatedCombineN,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(cats;kernel;Monoid;init;,[Default]), UsedName(reverse,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(combineAll,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(empty,[Default]), UsedName(cats;kernel;Semigroup;init;,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(given_Monoid_Event,[Default]), UsedName(combine,[Default]), UsedName(model$package$;given_Monoid_Event$;init;,[Default]), UsedName(intercalate,[Default]), UsedName($isInstanceOf,[Default]), UsedName(combineAllOption,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(isEmpty,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(combineN,[Default]), UsedName(java;lang;Object;init;,[Default])))))[0m
[0m[[0m[0mdebug[0m] [0m[0mThe model$package$ has the following implicit definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(given_Monoid_Event,[Default, Implicit]), UsedName(given_Monoid_Response,[Default, Implicit]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) model$package$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) model$package$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following member ref dependencies of model$package$ are invalidated:[0m
[0m[[0m[0mdebug[0m] [0m[0m	MonoidSpec[0m
[0m[[0m[0mdebug[0m] [0m[0mThe Event$Bar has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(Event$;Bar;init;,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(copy,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(y,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event$;Bar$;init;,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Event$Bar: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Event$Bar.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [Bar, y, apply][0m
[0m[[0m[0mdebug[0m] [0m[0mThe model$package$given_Monoid_Response$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(isInstanceOf,[Default]), UsedName(repeatedCombineN,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(cats;kernel;Monoid;init;,[Default]), UsedName(given_Monoid_Response,[Default]), UsedName(reverse,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(combineAll,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(model$package$;given_Monoid_Response$;init;,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(empty,[Default]), UsedName(cats;kernel;Semigroup;init;,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(combine,[Default]), UsedName(intercalate,[Default]), UsedName($isInstanceOf,[Default]), UsedName(combineAllOption,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(isEmpty,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(combineN,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) model$package$given_Monoid_Response$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) model$package$given_Monoid_Response$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [empty, given_Monoid_Response, combineAll][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Event$Bar$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(Event$;Bar;init;,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(copy,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(y,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event$;Bar$;init;,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Event$Bar$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Event$Bar$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [Bar, y, apply][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Event$Foo has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(Event$;Foo$;init;,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(Event$;Foo;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Event$Foo: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Event$Foo.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [x, Foo, apply][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Event has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(Start,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror$;Sum;init;,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(!=,[Default]), UsedName(fromOrdinal,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(Event$;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Event: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Event.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [Event, Bar, Start, Foo][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Response has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(bar,[Default]), UsedName(copy$default$2,[Default]), UsedName(copy$default$1,[Default]), UsedName(Response;init;,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(_2,[Default]), UsedName(y,[Default]), UsedName(apply,[Default]), UsedName(_1,[Default]), UsedName(foo,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Response: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Response.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [foo, x, y, bar, apply][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Event$Foo$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(Event$;Foo$;init;,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(Event$;Foo;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Event$Foo$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Event$Foo$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [x, Foo, apply][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Response$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(bar,[Default]), UsedName(copy$default$2,[Default]), UsedName(copy$default$1,[Default]), UsedName(Response;init;,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(_2,[Default]), UsedName(y,[Default]), UsedName(apply,[Default]), UsedName(_1,[Default]), UsedName(foo,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Response$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Response$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [foo, x, y, bar, apply][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Event$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(Start,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror$;Sum;init;,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(!=,[Default]), UsedName(fromOrdinal,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(Event$;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Event$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Event$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [Event, Bar, Start, Foo][0m
[0m[[0m[0mdebug[0m] [0m[0mThe model$package$given_Monoid_Event$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(isInstanceOf,[Default]), UsedName(repeatedCombineN,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(cats;kernel;Monoid;init;,[Default]), UsedName(reverse,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(combineAll,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(empty,[Default]), UsedName(cats;kernel;Semigroup;init;,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(given_Monoid_Event,[Default]), UsedName(combine,[Default]), UsedName(model$package$;given_Monoid_Event$;init;,[Default]), UsedName(intercalate,[Default]), UsedName($isInstanceOf,[Default]), UsedName(combineAllOption,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(isEmpty,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(combineN,[Default]), UsedName(java;lang;Object;init;,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) model$package$given_Monoid_Event$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) model$package$given_Monoid_Event$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of MonoidSpec: [empty, combineAll, given_Monoid_Event][0m
[0m[[0m[0mdebug[0m] [0m[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial source changes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	removed: Set(${BASE}/src/test/scala/MonoidSpec.scala, ${BASE}/src/test/scala/MainSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	added: Set(${BASE}/src/test/scala/ModelSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	modified: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidated products: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mExternal API changes: API Changes: Set(NamesChange(model$package$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(model$package$;init;,[Default]), UsedName(given_Monoid_Event,[Default, Implicit]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(model$package,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(given_Monoid_Response,[Default, Implicit]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event$Bar,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(Event$;Bar;init;,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(copy,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(y,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event$;Bar$;init;,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(model$package$given_Monoid_Response$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(repeatedCombineN,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(cats;kernel;Monoid;init;,[Default]), UsedName(given_Monoid_Response,[Default]), UsedName(reverse,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(combineAll,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(model$package$;given_Monoid_Response$;init;,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(empty,[Default]), UsedName(cats;kernel;Semigroup;init;,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(combine,[Default]), UsedName(intercalate,[Default]), UsedName($isInstanceOf,[Default]), UsedName(combineAllOption,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(isEmpty,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(combineN,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event$Bar$,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(Event$;Bar;init;,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(copy,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(y,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event$;Bar$;init;,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event$Foo,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(Event$;Foo$;init;,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(Event$;Foo;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Event,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(Start,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror$;Sum;init;,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(!=,[Default]), UsedName(fromOrdinal,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(Event$;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Response,ModifiedNames(changes = UsedName(bar,[Default]), UsedName(copy$default$2,[Default]), UsedName(copy$default$1,[Default]), UsedName(Response;init;,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(_2,[Default]), UsedName(y,[Default]), UsedName(apply,[Default]), UsedName(_1,[Default]), UsedName(foo,[Default]))), NamesChange(Event$Foo$,ModifiedNames(changes = UsedName(copy$default$1,[Default]), UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(scala;deriving;Mirror$;Product;init;,[Default]), UsedName(!=,[Default]), UsedName(canEqual,[Default]), UsedName(apply,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(productElement,[Default]), UsedName(Event$;Foo$;init;,[Default]), UsedName(==,[Default]), UsedName(_1,[Default]), UsedName(Event$;Foo;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(productElementName,[Default]), UsedName(unapply,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName(fromProduct,[Default]), UsedName(productPrefix,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName(productArity,[Default]), UsedName($asInstanceOf,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(Response$,ModifiedNames(changes = UsedName(bar,[Default]), UsedName(copy$default$2,[Default]), UsedName(copy$default$1,[Default]), UsedName(Response;init;,[Default]), UsedName(x,[Default]), UsedName(copy,[Default]), UsedName(_2,[Default]), UsedName(y,[Default]), UsedName(apply,[Default]), UsedName(_1,[Default]), UsedName(foo,[Default]))), NamesChange(Event$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(MirroredLabel,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(Foo,[Default]), UsedName(equals,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(Start,[Default]), UsedName(finalize,[Default]), UsedName(Event;init;,[Default]), UsedName(notify,[Default]), UsedName(MirroredElemLabels,[Default]), UsedName(ordinal,[Default]), UsedName(hashCode,[Default]), UsedName(Bar,[Default]), UsedName(getClass,[Default]), UsedName(scala;deriving;Mirror$;Sum;init;,[Default]), UsedName(scala;deriving;Mirror;init;,[Default]), UsedName(!=,[Default]), UsedName(fromOrdinal,[Default]), UsedName(MirroredMonoType,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(==,[Default]), UsedName(Event$;init;,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(scala;reflect;Enum;init;,[Default]), UsedName($isInstanceOf,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(Event,[Default]), UsedName(java;lang;Object;init;,[Default]))), NamesChange(model$package$given_Monoid_Event$,ModifiedNames(changes = UsedName(isInstanceOf,[Default]), UsedName(repeatedCombineN,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(equals,[Default]), UsedName(cats;kernel;Monoid;init;,[Default]), UsedName(reverse,[Default]), UsedName(toString,[Default]), UsedName(asInstanceOf,[Default]), UsedName(finalize,[Default]), UsedName(notify,[Default]), UsedName(combineAll,[Default]), UsedName(hashCode,[Default]), UsedName(getClass,[Default]), UsedName(!=,[Default]), UsedName(##,[Default]), UsedName(synchronized,[Default]), UsedName(empty,[Default]), UsedName(cats;kernel;Semigroup;init;,[Default]), UsedName(==,[Default]), UsedName(clone,[Default]), UsedName(eq,[Default]), UsedName(given_Monoid_Event,[Default]), UsedName(combine,[Default]), UsedName(model$package$;given_Monoid_Event$;init;,[Default]), UsedName(intercalate,[Default]), UsedName($isInstanceOf,[Default]), UsedName(combineAllOption,[Default]), UsedName(ne,[Default]), UsedName(wait,[Default]), UsedName(isEmpty,[Default]), UsedName(notifyAll,[Default]), UsedName($asInstanceOf,[Default]), UsedName(combineN,[Default]), UsedName(java;lang;Object;init;,[Default]))))[0m
[0m[[0m[0mdebug[0m] [0m[0mModified binary dependencies: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial directly invalidated classes: Set(MonoidSpec, MainSpec)[0m
[0m[[0m[0mdebug[0m] [0m[0mSources indirectly invalidated by:[0m
[0m[[0m[0mdebug[0m] [0m[0m	product: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	binary dep: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	external source: Set(MonoidSpec)[0m
[0m[[0m[0mdebug[0m] [0m[0mall 1 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: MonoidSpec, MainSpec[0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-sbt-bridge/3.3.4/scala3-sbt-bridge-3.3.4.jar.[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] Running cached compiler 57b037fe for Scala Compiler version 3.3.4[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes:/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0mScala compilation took 1.439778625 s[0m
[0m[[0m[0mdebug[0m] [0m[0mdone compiling[0m
