[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 1 products, 1 sources, 8 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/src/test/scala/MonoidSpec.scala, ${BASE}/src/test/scala/MainSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/src/test/scala/MonoidSpec.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial source changes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	removed: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	added: Set(${BASE}/src/test/scala/MonoidSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	modified: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidated products: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mExternal API changes: API Changes: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mModified binary dependencies: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial directly invalidated classes: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mSources indirectly invalidated by:[0m
[0m[[0m[0mdebug[0m] [0m[0m	product: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	binary dep: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	external source: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mAll initially invalidated classes: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mAll initially invalidated sources:Set(${BASE}/src/test/scala/MonoidSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: [0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-sbt-bridge/3.3.4/scala3-sbt-bridge-3.3.4.jar.[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] Running cached compiler 3049ebae for Scala Compiler version 3.3.4[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes:/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0mScala compilation took 0.56904975 s[0m
[0m[[0m[0mdebug[0m] [0m[0mdone compiling[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidating (transitively) by inheritance from MonoidSpec...[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: MonoidSpec[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidated by transitive inheritance dependency: Set(MonoidSpec)[0m
[0m[[0m[0mdebug[0m] [0m[0mChange NamesChange(MonoidSpec,ModifiedNames(changes = UsedName(testDataFor$default$2,[Default]), UsedName(compile,[Default]), UsedName(##,[Default]), UsedName(convertToInAndIgnoreMethodsAfterTaggedAs,[Implicit]), UsedName(determined,[Default]), UsedName(org;scalatest;flatspec;AnyFlatSpecLike;init;,[Default]), UsedName(IgnoreVerbString,[Default]), UsedName(execute$default$3,[Default]), UsedName(org;scalatest;matchers;dsl;MatcherWords;init;,[Default]), UsedName(typeCheck,[Default]), UsedName(org;scalatest;Assertions;init;,[Default]), UsedName(CheckingEqualizer,[Default]), UsedName(StringCanWrapperForVerb,[Default]), UsedName(IgnoreWord,[Default]), UsedName(java;io;Serializable;init;,[Default]), UsedName(theSameElementsInOrderAs,[Default]), UsedName(MonoidSpec;init;,[Default]), UsedName(ResultOfStartWithWordForCollectedString,[Default]), UsedName(defaultEquality,[Default]), UsedName(NoArgTest,[Default]), UsedName(oneElementOf,[Default]), UsedName(allOf,[Default]), UsedName(of,[Default]), UsedName(writable,[Default]), UsedName(convertSymbolToHavePropertyMatcherGenerator,[Implicit]), UsedName(suiteId,[Default]), UsedName(theSameElementsAs,[Default]), UsedName(TheyVerbStringTaggedAs,[Default]), UsedName(shorthandTestRegistrationFunction,[Implicit]), UsedName(typeCheckedConstraint,[Default]), UsedName(shouldBe,[Default]), UsedName(the,[Default]), UsedName(convertToStringMustWrapperForVerb,[Implicit]), UsedName(styleName,[Default]), UsedName(not,[Default]), UsedName(noneOf,[Default]), UsedName(no,[Default]), UsedName(org$scalatest$Assertions$$inline$assertThrowsImpl,[Default]), UsedName(ResultOfHaveWordForExtent,[Default]), UsedName(org;scalatest;Notifying;init;,[Default]), UsedName(run,[Default]), UsedName(unconstrainedEquality,[Default, Implicit]), UsedName(org$scalatest$Assertions$$inline$pendingUntilFixedImpl,[Default]), UsedName(they,[Default]), UsedName(testNames,[Default]), UsedName(convertToCheckingEqualizer,[Default]), UsedName(readable,[Default]), UsedName(nestedSuites,[Default]), UsedName(AnyShouldWrapper,[Default]), UsedName(Equalizer,[Default]), UsedName(after,[Default]), UsedName(definedAt,[Default]), UsedName(it,[Default]), UsedName(convertToEqualizer,[Default, Implicit]), UsedName(length,[Default]), UsedName(StringShouldWrapperForVerb,[Default]), UsedName(DecidedByEquality,[Default]), UsedName(assume,[Default]), UsedName($asInstanceOf,[Default]), UsedName(a,[Default]), UsedName(all,[Default]), UsedName(shouldEqual,[Default]), UsedName(tags,[Default]), UsedName(info,[Default]), UsedName(pending,[Default]), UsedName(every,[Default]), UsedName(lowPriorityTypeCheckedConstraint,[Default]), UsedName($isInstanceOf,[Default]), UsedName(value,[Default]), UsedName(org;scalatest;TestRegistration;init;,[Default]), UsedName(exist,[Default]), UsedName(fail,[Default]), UsedName(wait,[Default]), UsedName(endWith,[Default]), UsedName(org;scalactic;TripleEqualsSupport;init;,[Default]), UsedName(ItVerbStringTaggedAs,[Default]), UsedName(ResultOfIncludeWordForCollectedString,[Default]), UsedName(between,[Default]), UsedName(include,[Default]), UsedName(asInstanceOf,[Default]), UsedName(!=,[Default]), UsedName(IgnoreVerbStringTaggedAs,[Default]), UsedName(atMostOneOf,[Default]), UsedName(contain,[Default]), UsedName(pipeChar,[Default]), UsedName(convertNumericToPlusOrMinusWrapper,[Implicit]), UsedName(notifyAll,[Default]), UsedName(ResultOfBeWordForCollectedArray,[Default]), UsedName(message,[Default]), UsedName(assertDoesNotCompile,[Default]), UsedName(allElementsOf,[Default]), UsedName(fullyMatch,[Default]), UsedName(org$scalatest$flatspec$AnyFlatSpecLike$$inline$registerTestToRun,[Default]), UsedName(org;scalatest;Documenting;init;,[Default]), UsedName(clone,[Default]), UsedName(inOrder,[Default]), UsedName(atLeastOneOf,[Default]), UsedName(execute$default$1,[Default]), UsedName(atMostOneElementOf,[Default]), UsedName(pendingUntilFixed,[Default]), UsedName(ResultOfCollectedAny,[Default]), UsedName(note,[Default]), UsedName(exactly,[Default]), UsedName(succeed,[Default]), UsedName(org;scalatest;verbs;MustVerb;init;,[Default]), UsedName(markup,[Default]), UsedName(withClue,[Default]), UsedName(doCollected,[Default]), UsedName(an,[Default]), UsedName(sorted,[Default]), UsedName(DeterminedWord,[Default]), UsedName(convertToStringCanWrapper,[Implicit]), UsedName(ValueWord,[Default]), UsedName(AnWord,[Default]), UsedName(runTests,[Default]), UsedName(rerunner,[Default]), UsedName(RegexWord,[Default]), UsedName(convertEquivalenceToAToBConstraint,[Default]), UsedName(withGroup,[Default]), UsedName(expectedTestCount,[Default]), UsedName(atLeastOneElementOf,[Default]), UsedName(testDataFor,[Default]), UsedName(ResultOfBeWordForCollectedAny,[Default]), UsedName(withFixture,[Default]), UsedName(DeterminedByEquivalence,[Default]), UsedName(MonoidSpec,[Default]), UsedName(==,[Default]), UsedName(KeyWord,[Default]), UsedName(ResultOfNotWordForCollectedAny,[Default]), UsedName(Collected,[Default]), UsedName(isInstanceOf,[Default]), UsedName(ResultOfContainWordForCollectedAny,[Default]), UsedName(<,[Default]), UsedName(be,[Default]), UsedName(atLeast,[Default]), UsedName(<=,[Default]), UsedName(only,[Default]), UsedName(assertCompiles,[Default]), UsedName(have,[Default]), UsedName(shouldNot,[Default]), UsedName(org;scalatest;verbs;ShouldVerb;init;,[Default]), UsedName(org$scalatest$Assertions$$inline$cancelImpl,[Default]), UsedName(empty,[Default]), UsedName(inOrderOnly,[Default]), UsedName(org$scalatest$flatspec$AnyFlatSpecLike$$inline$registerTestImpl,[Default]), UsedName(ItWord,[Default]), UsedName(org$scalatest$Assertions$$inline$newAssertionFailedException,[Default]), UsedName(InAndIgnoreMethods,[Default]), UsedName(inOrderElementsOf,[Default]), UsedName(org$scalatest$flatspec$AnyFlatSpecLike$$super$run,[Default]), UsedName(ResultOfHaveWordForCollectedExtent,[Default]), UsedName(convertToInAndIgnoreMethods,[Implicit]), UsedName(intercept,[Default]), UsedName(org;scalatest;flatspec;AnyFlatSpec;init;,[Default]), UsedName(BehaviorWord,[Default]), UsedName(equal,[Default]), UsedName(ResultOfBeWordForAny,[Default]), UsedName(>,[Default]), UsedName(>=,[Default]), UsedName(org;scalatest;Suite;init;,[Default]), UsedName(ResultOfFullyMatchWordForString,[Default]), UsedName(runNestedSuites,[Default]), UsedName(synchronized,[Default]), UsedName(conversionCheckedConstraint,[Default]), UsedName(trap,[Default]), UsedName(convertEquivalenceToAToBConversionConstraint,[Default]), UsedName(behave,[Default]), UsedName(runTest,[Default]), UsedName(defined,[Default]), UsedName(execute$default$4,[Default]), UsedName(hashCode,[Default]), UsedName(ResultOfEndWithWordForString,[Default]), UsedName(UseDefaultAssertions,[Default, Implicit]), UsedName(java;lang;Object;init;,[Default]), UsedName(assertResult,[Default]), UsedName(execute,[Default]), UsedName(execute$default$2,[Default]), UsedName(stripMargin,[Default]), UsedName(assertTypeError,[Default]), UsedName(ItVerbString,[Default]), UsedName(TheSameInstanceAsPhrase,[Default]), UsedName(newAssertionFailedException,[Default]), UsedName(newTestCanceledException,[Default]), UsedName(org;scalatest;Informing;init;,[Default]), UsedName(org;scalactic;Tolerance;init;,[Default]), UsedName(regex,[Default]), UsedName(registerIgnoredTest,[Default]), UsedName(equals,[Default]), UsedName(should,[Default]), UsedName(ShouldMethodHelperClass,[Default]), UsedName(ResultOfFullyMatchWordForCollectedString,[Default]), UsedName(atMost,[Default]), UsedName(convertEquivalenceToBToAConversionConstraint,[Default]), UsedName(org;scalactic;TripleEquals;init;,[Default]), UsedName(cancel,[Default]), UsedName(PlusOrMinusWrapper,[Default]), UsedName(org$scalatest$Assertions$$inline$failImpl,[Default]), UsedName(notify,[Default]), UsedName(ne,[Default]), UsedName(org$scalatest$flatspec$AnyFlatSpecLike$$inline$registerIgnoredTestImpl,[Default]), UsedName(behavior,[Default]), UsedName(DecidedWord,[Default]), UsedName(TheyVerbString,[Default]), UsedName(org$scalatest$Assertions$$inline$interceptImpl,[Default]), UsedName(createCatchReporter,[Default]), UsedName(convertEquivalenceToBToAConstraint,[Default]), UsedName(execute$default$7,[Default]), UsedName(TheyWord,[Default]), UsedName(oneOf,[Default]), UsedName(key,[Default]), UsedName(getClass,[Default]), UsedName(decided,[Default]), UsedName(ignore,[Default]), UsedName(StringMustWrapperForVerb,[Default]), UsedName(finalize,[Default]), UsedName(shorthandSharedTestRegistrationFunction,[Implicit]), UsedName(assertThrows,[Default]), UsedName(noElementsOf,[Default]), UsedName(TheAfterWord,[Default]), UsedName(thrownBy,[Default]), UsedName(!==,[Default]), UsedName(alert,[Default]), UsedName(withGroups,[Default]), UsedName(registerTest,[Default]), UsedName(theSameInstanceAs,[Default]), UsedName(noException,[Default]), UsedName(org;scalatest;verbs;CanVerb;init;,[Default]), UsedName(HavePropertyMatcherGenerator,[Default]), UsedName(execute$default$6,[Default]), UsedName(AWord,[Default]), UsedName(eq,[Default]), UsedName(suiteName,[Default]), UsedName(ResultOfEndWithWordForCollectedString,[Default]), UsedName(toString,[Default]), UsedName(size,[Default]), UsedName(matchPattern,[Default]), UsedName(execute$default$5,[Default]), UsedName(startWith,[Default]), UsedName(org$scalatest$flatspec$AnyFlatSpecLike$$inline$registerTestToIgnore,[Default]), UsedName(ResultOfStartWithWordForString,[Default]), UsedName(org$scalatest$Assertions$$inline$newTestCanceledException,[Default]), UsedName(org;scalatest;TestSuite;init;,[Default]), UsedName(org$scalatest$Assertions$$inline$assertResultImpl,[Default]), UsedName(InAndIgnoreMethodsAfterTaggedAs,[Default]), UsedName(assert,[Default]), UsedName(ResultOfIncludeWordForString,[Default]), UsedName(org;scalactic;Explicitly;init;,[Default]), UsedName(===,[Default]), UsedName(convertToStringShouldWrapperForVerb,[Implicit]), UsedName(lowPriorityConversionCheckedConstraint,[Default]), UsedName(org;scalatest;matchers;should;Matchers;init;,[Default]), UsedName(org;scalatest;Alerting;init;,[Default]))) invalidates 1 classes due to The MonoidSpec has the following implicit definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(convertToInAndIgnoreMethodsAfterTaggedAs,[Implicit]), UsedName(convertSymbolToHavePropertyMatcherGenerator,[Implicit]), UsedName(shorthandTestRegistrationFunction,[Implicit]), UsedName(convertToStringMustWrapperForVerb,[Implicit]), UsedName(unconstrainedEquality,[Default, Implicit]), UsedName(convertToEqualizer,[Default, Implicit]), UsedName(convertNumericToPlusOrMinusWrapper,[Implicit]), UsedName(convertToStringCanWrapper,[Implicit]), UsedName(convertToInAndIgnoreMethods,[Implicit]), UsedName(UseDefaultAssertions,[Default, Implicit]), UsedName(shorthandSharedTestRegistrationFunction,[Implicit]), UsedName(convertToStringShouldWrapperForVerb,[Implicit]).[0m
[0m[[0m[0mdebug[0m] [0m[0m  > by transitive inheritance: Set(MonoidSpec)[0m
[0m[[0m[0mdebug[0m] [0m[0m  > [0m
[0m[[0m[0mdebug[0m] [0m[0m  > [0m
[0m[[0m[0mdebug[0m] [0m[0m  > [0m
[0m[[0m[0mdebug[0m] [0m[0m        [0m
[0m[[0m[0mdebug[0m] [0m[0mNew invalidations:[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: [0m
[0m[[0m[0mdebug[0m] [0m[0mPreviously invalidated, but (transitively) depend on new invalidations:[0m
[0m[[0m[0mdebug[0m] [0m[0mFinal step, transitive dependencies:[0m
[0m[[0m[0mdebug[0m] [0m[0m	Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: MonoidSpec[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidated macros due to upstream dependencies change: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNo classes were invalidated.[0m
