[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 10 products, 1 sources, 8 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/src/test/scala/ModelSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(), removed = Set(), changed = Set(${BASE}/src/test/scala/ModelSpec.scala), unmodified = ...),Set(),Set(),API Changes: Set(NamesChange(FSM,ModifiedNames(changes = UsedName(getCurrentState,[Default]), Used<PERSON>ame(reset,[Default]), <PERSON><PERSON><PERSON>(run,[Default]), UsedName(canAccept,[Default]))), NamesChange(Interpreter,ModifiedNames(changes = UsedName(op0,[Default]), UsedName(op1,[Default]))), NamesChange(ConcreteInterpreter$,ModifiedNames(changes = UsedName(op0,[Default]), UsedName(op1,[Default])))))[0m
[0m[[0m[0mdebug[0m] [0m[0mThe FSM has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(getCurrentState,[Default]), UsedName(reset,[Default]), UsedName(run,[Default]), UsedName(canAccept,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) FSM: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) FSM.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of ModelSpec: [run][0m
[0m[[0m[0mdebug[0m] [0m[0mThe Interpreter has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(op0,[Default]), UsedName(op1,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) Interpreter: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) Interpreter.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of ModelSpec: [op0, op1][0m
[0m[[0m[0mdebug[0m] [0m[0mThe ConcreteInterpreter$ has the following regular definitions changed:[0m
[0m[[0m[0mdebug[0m] [0m[0m	UsedName(op0,[Default]), UsedName(op1,[Default]).[0m
[0m[[0m[0mdebug[0m] [0m[0mAll member reference dependencies will be considered within this context.[0m
[0m[[0m[0mdebug[0m] [0m[0mFiles invalidated by inheriting from (external) ConcreteInterpreter$: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mNow invalidating by inheritance (internally).[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting direct dependencies of all classes transitively invalidated by inheritance.[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting classes that directly depend on (external) ConcreteInterpreter$.[0m
[0m[[0m[0mdebug[0m] [0m[0mThe following modified names cause invalidation of ModelSpec: [op0, op1][0m
[0m[[0m[0mdebug[0m] [0m[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial source changes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	removed: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	added: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	modified: Set(${BASE}/src/test/scala/ModelSpec.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0mInvalidated products: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mExternal API changes: API Changes: Set(NamesChange(FSM,ModifiedNames(changes = UsedName(getCurrentState,[Default]), UsedName(reset,[Default]), UsedName(run,[Default]), UsedName(canAccept,[Default]))), NamesChange(Interpreter,ModifiedNames(changes = UsedName(op0,[Default]), UsedName(op1,[Default]))), NamesChange(ConcreteInterpreter$,ModifiedNames(changes = UsedName(op0,[Default]), UsedName(op1,[Default]))))[0m
[0m[[0m[0mdebug[0m] [0m[0mModified binary dependencies: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial directly invalidated classes: Set(ModelSpec)[0m
[0m[[0m[0mdebug[0m] [0m[0mSources indirectly invalidated by:[0m
[0m[[0m[0mdebug[0m] [0m[0m	product: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	binary dep: Set()[0m
[0m[[0m[0mdebug[0m] [0m[0m	external source: Set(ModelSpec)[0m
[0m[[0m[0mdebug[0m] [0m[0mall 1 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: ModelSpec[0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-sbt-bridge/3.3.4/scala3-sbt-bridge-3.3.4.jar.[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] Running cached compiler 51ce9ae1 for Scala Compiler version 3.3.4[0m
[0m[[0m[0mdebug[0m] [0m[0m[zinc] The Scala compiler is invoked with:[0m
[0m[[0m[0mdebug[0m] [0m[0m	-classpath[0m
[0m[[0m[0mdebug[0m] [0m[0m	/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes:/Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/classes:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala3-library_3/3.3.4/scala3-library_3-3.3.4.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_3/2.12.0/cats-core_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect_3/3.5.7/cats-effect_3-3.5.7.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest_3/3.2.19/scalatest_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatestplus/scalacheck-1-17_3/3.2.18.0/scalacheck-1-17_3-3.2.18.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.14/scala-library-2.13.14.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_3/2.12.0/cats-kernel_3-2.12.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-kernel_3/3.5.7/cats-effect-kernel_3-3.5.7.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-effect-std_3/3.5.7/cats-effect-std_3-3.5.7.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-core_3/3.2.19/scalatest-core_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-featurespec_3/3.2.19/scalatest-featurespec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-flatspec_3/3.2.19/scalatest-flatspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-freespec_3/3.2.19/scalatest-freespec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funsuite_3/3.2.19/scalatest-funsuite_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-funspec_3/3.2.19/scalatest-funspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-propspec_3/3.2.19/scalatest-propspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-refspec_3/3.2.19/scalatest-refspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-wordspec_3/3.2.19/scalatest-wordspec_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-diagrams_3/3.2.19/scalatest-diagrams_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-matchers-core_3/3.2.19/scalatest-matchers-core_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-shouldmatchers_3/3.2.19/scalatest-shouldmatchers_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-mustmatchers_3/3.2.19/scalatest-mustmatchers_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalacheck/scalacheck_3/1.17.0/scalacheck_3-1.17.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalactic/scalactic_3/3.2.19/scalactic_3-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalatest/scalatest-compatible/3.2.19/scalatest-compatible-3.2.19.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_3/2.1.0/scala-xml_3-2.1.0.jar:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar[0m
[0m[[0m[0mdebug[0m] [0m[0mScala compilation took 1.312978042 s[0m
[0m[[0m[0mdebug[0m] [0m[0mdone compiling[0m
