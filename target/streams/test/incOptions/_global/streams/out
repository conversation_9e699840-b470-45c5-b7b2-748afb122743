[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0m	MonoidSpec.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	MainSpec.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	MonoidSpec.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	MainSpec.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0m	MonoidSpec.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	MainSpec.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	MonoidSpec.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	MainSpec.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mRegistering generated classes:[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$6.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$3.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$7.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$4.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$8.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$2.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$9.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$5.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$1.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$6.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$3.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$7.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$4.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$8.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$2.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$9.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec$$anon$5.tasty[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: /Users/<USER>/SourceCode/cats-state/target/scala-3.3.4/test-classes.bak[0m
