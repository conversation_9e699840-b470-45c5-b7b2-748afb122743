[0m[[0m[0mdebug[0m] [0m[0mRunning TaskDef(MainSpec, org.scalatest.tools.Framework$$anon$1@3db743af, false, [SuiteSelector])[0m
[0m[[0m[0minfo[0m] [0m[0m[32mMainSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mStack operations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work correctly with State monad[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Option[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Either[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mRun completed in 197 milliseconds.[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTotal number of tests run: 3[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mSuites: completed 1, aborted 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTests: succeeded 3, failed 0, canceled 0, ignored 0, pending 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mAll tests passed.[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mPassed tests:[0m
[0m[[0m[0mdebug[0m] [0m[0m	MainSpec[0m
