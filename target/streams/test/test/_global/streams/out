[0m[[0m[0mdebug[0m] [0m[0mRunning TaskDef(ModelSpec, org.scalatest.tools.Framework$$anon$1@52875e1, false, [SuiteSelector])[0m
[0m[[0m[0minfo[0m] [0m[0m[32mModelSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mResponse[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be created with string and int values[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support equality[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support copy method[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mApiOneResult[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should extend Result trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support equality[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mApiTwoResult[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should extend Result trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support equality[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mConcreteInterpreter[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should implement op0 correctly[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should implement op1 correctly[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should return consistent results regardless of input[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle empty string input[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mFSM[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine results from both operations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with custom interpreter[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle different inputs with custom interpreter[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle edge cases[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mInterpreter trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should allow for different implementations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support stateful interpreters[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mResult trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be implemented by both result types[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support pattern matching[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mIntegration[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work end-to-end with multiple FSM instances[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mRun completed in 152 milliseconds.[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTotal number of tests run: 20[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mSuites: completed 1, aborted 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTests: succeeded 20, failed 0, canceled 0, ignored 0, pending 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mAll tests passed.[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mPassed tests:[0m
[0m[[0m[0mdebug[0m] [0m[0m	ModelSpec[0m
