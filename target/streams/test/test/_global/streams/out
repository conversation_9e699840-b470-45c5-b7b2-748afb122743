[0m[[0m[0mdebug[0m] [0m[0mRunning TaskDef(ModelSpec, org.scalatest.tools.Framework$$anon$1@46107406, false, [SuiteSelector])[0m
[0m[[0m[0minfo[0m] [0m[0m[32mModelSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mResponse[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be created with string and int values[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support equality[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support copy method[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mApiOneResult[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should extend Result trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support equality[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mApiTwoResult[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should extend Result trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support equality[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mEvent.Begin[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should contain pid data[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mEvent.Foo[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should contain string data[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mEvent.Bar[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should contain integer data[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mState enum[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should have Start, Processing, and Stopped states[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mConcreteInterpreter[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should implement op0 correctly[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should implement op1 correctly[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should return consistent results regardless of input[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle empty string input[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mFSM[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should start in Start state[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle Begin events from Start state[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle Foo events from Processing state[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle Bar events from Processing state and transition to Stopped[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should reject events in Stopped state[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should reject invalid events for current state[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should reset to Start state[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should correctly report canAccept for different states[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with custom interpreter for Begin events[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with custom interpreter for Foo events[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with custom interpreter for Bar events[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[31m- should process a valid event sequence correctly *** FAILED ***[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[31m  Response("", 10) was not equal to Response("", 2) (ModelSpec.scala:254)[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should handle edge cases[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mInterpreter trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should allow for different implementations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support stateful interpreters[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mResult trait[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be implemented by both result types[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should support pattern matching[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mIntegration[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work end-to-end with multiple FSM instances[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mRun completed in 300 milliseconds.[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTotal number of tests run: 33[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mSuites: completed 1, aborted 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTests: succeeded 32, failed 1, canceled 0, ignored 0, pending 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[31m*** 1 TEST FAILED ***[0m[0m
[0m[[0m[31merror[0m] [0m[0mFailed tests:[0m
[0m[[0m[31merror[0m] [0m[0m	ModelSpec[0m
[0m[[0m[31merror[0m] [0m[0m(Test / [31mtest[0m) sbt.TestsFailedException: Tests unsuccessful[0m
