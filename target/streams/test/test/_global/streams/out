[0m[[0m[0mdebug[0m] [0m[0mRunning TaskDef(MainSpec, org.scalatest.tools.Framework$$anon$1@7a63007, false, [SuiteSelector])[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning TaskDef(MonoidSpec, org.scalatest.tools.Framework$$anon$1@7a63007, false, [SuiteSelector])[0m
[0m[[0m[0minfo[0m] [0m[0m[32mMainSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mStack operations[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work correctly with State monad[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Option[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mCats syntax[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with Either[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mMonoidSpec:[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mEvent Monoid[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should have Start as empty element[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine with Start as identity[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine Foo events by concatenating strings[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine Bar events by adding integers[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should give precedence to Foo when combining Foo and Bar[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be associative[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mResponse Monoid[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should have empty Response with Start events[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should combine Responses by combining their fields[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with empty as identity[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should be associative[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32m- should work with combineAll for multiple responses[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mRun completed in 89 milliseconds.[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTotal number of tests run: 14[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mSuites: completed 2, aborted 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[36mTests: succeeded 14, failed 0, canceled 0, ignored 0, pending 0[0m[0m
[0m[[0m[0minfo[0m] [0m[0m[32mAll tests passed.[0m[0m
[0m[[0m[0mdebug[0m] [0m[0mPassed tests:[0m
[0m[[0m[0mdebug[0m] [0m[0m	MainSpec[0m
[0m[[0m[0mdebug[0m] [0m[0m	MonoidSpec[0m
