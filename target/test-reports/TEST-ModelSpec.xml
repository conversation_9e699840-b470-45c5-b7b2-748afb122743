<?xml version='1.0' encoding='UTF-8'?>
<testsuite hostname="Johnnies-MacBook-Pro-2.local" name="ModelSpec" tests="33" errors="0" failures="1" skipped="0" time="0.159" timestamp="2025-06-26T16:08:46">
          <properties>
      <property name="java.specification.version" value="23"/><property name="sun.jnu.encoding" value="UTF-8"/><property name="java.class.path" value="/opt/homebrew/Cellar/sbt/1.10.11/libexec/bin/sbt-launch.jar"/><property name="java.vm.vendor" value="Homebrew"/><property name="sun.arch.data.model" value="64"/><property name="jline.shutdownhook" value="false"/><property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/><property name="user.timezone" value="Europe/London"/><property name="java.vm.specification.version" value="23"/><property name="os.name" value="Mac OS X"/><property name="sun.java.launcher" value="SUN_STANDARD"/><property name="user.country" value="GB"/><property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/><property name="sun.java.command" value="/opt/homebrew/Cellar/sbt/1.10.11/libexec/bin/sbt-launch.jar"/><property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/><property name="jdk.debug" value="release"/><property name="sun.cpu.endian" value="little"/><property name="user.home" value="/Users/<USER>"/><property name="user.language" value="en"/><property name="sbt.script" value="/opt/homebrew/Cellar/sbt/1.10.11/libexec/bin/sbt"/><property name="java.specification.vendor" value="Oracle Corporation"/><property name="sbt.ipcsocket.tmpdir" value="/var/folders/qj/std_nz6x3cb2vytf8lph0jw00000gn/T/.sbteee92d51/ipcsocket"/><property name="swoval.tmpdir" value="/var/folders/qj/std_nz6x3cb2vytf8lph0jw00000gn/T/.sbteee92d51/swoval"/><property name="java.version.date" value="2025-01-21"/><property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/><property name="file.separator" value="/"/><property name="java.vm.compressedOopsMode" value="Zero based"/><property name="line.separator" value="
"/><property name="java.vm.specification.vendor" value="Oracle Corporation"/><property name="java.specification.name" value="Java Platform API Specification"/><property name="apple.awt.application.name" value="Boot"/><property name="jline.esc.timeout" value="0"/><property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/><property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/><property name="java.runtime.version" value="23.0.2"/><property name="user.name" value="johnnie"/><property name="stdout.encoding" value="UTF-8"/><property name="path.separator" value=":"/><property name="os.version" value="15.5"/><property name="jna.nosys" value="true"/><property name="java.runtime.name" value="OpenJDK Runtime Environment"/><property name="log4j.ignoreTCL" value="true"/><property name="file.encoding" value="UTF-8"/><property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/><property name="java.vendor.version" value="Homebrew"/><property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/><property name="java.io.tmpdir" value="/var/folders/qj/std_nz6x3cb2vytf8lph0jw00000gn/T/"/><property name="java.version" value="23.0.2"/><property name="user.dir" value="/Users/<USER>/SourceCode/cats-state"/><property name="os.arch" value="aarch64"/><property name="java.vm.specification.name" value="Java Virtual Machine Specification"/><property name="native.encoding" value="UTF-8"/><property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/><property name="java.vm.info" value="mixed mode, sharing"/><property name="stderr.encoding" value="UTF-8"/><property name="java.vendor" value="Homebrew"/><property name="java.vm.version" value="23.0.2"/><property name="scala.ext.dirs" value="/Users/<USER>/.sbt/1.0/java9-rt-ext-homebrew_23_0_2"/><property name="sun.io.unicode.encoding" value="UnicodeBig"/><property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/><property name="java.class.version" value="67.0"/>
    </properties>
          <testcase classname="ModelSpec" name="Response should be created with string and int values" time="0.005">
                      
                    </testcase><testcase classname="ModelSpec" name="Response should support equality" time="0.002">
                      
                    </testcase><testcase classname="ModelSpec" name="Response should support copy method" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiOneResult should extend Result trait" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiOneResult should support equality" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiTwoResult should extend Result trait" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiTwoResult should support equality" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Event.Begin should contain pid data" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="Event.Foo should contain string data" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Event.Bar should contain integer data" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="State enum should have Start, Processing, and Stopped states" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should implement op0 correctly" time="0.135">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should implement op1 correctly" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should return consistent results regardless of input" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should handle empty string input" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should start in Start state" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should handle Begin events from Start state" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should handle Foo events from Processing state" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should handle Bar events from Processing state and transition to Stopped" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should reject events in Stopped state" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should reject invalid events for current state" time="0.003">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should reset to Start state" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should correctly report canAccept for different states" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should work with custom interpreter for Begin events" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should work with custom interpreter for Foo events" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should work with custom interpreter for Bar events" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should process a valid event sequence correctly" time="0.005">
                      <failure message="Response(&quot;&quot;, 10) was not equal to Response(&quot;&quot;, 2)" type="org.scalatest.exceptions.TestFailedException">org.scalatest.exceptions.TestFailedException: Response(&quot;&quot;, 10) was not equal to Response(&quot;&quot;, 2)
	at org.scalatest.matchers.MatchersHelper$.indicateFailure(MatchersHelper.scala:392)
	at org.scalatest.matchers.should.Matchers.shouldBe(Matchers.scala:7017)
	at org.scalatest.matchers.should.Matchers.shouldBe$(Matchers.scala:1808)
	at ModelSpec.shouldBe(ModelSpec.scala:6)
	at ModelSpec.testFun$proxy27$1(ModelSpec.scala:254)
	at ModelSpec.$init$$$anonfun$27(ModelSpec.scala:239)
	at org.scalatest.Transformer.apply$$anonfun$1(Transformer.scala:22)
	at org.scalatest.OutcomeOf.outcomeOf(OutcomeOf.scala:85)
	at org.scalatest.OutcomeOf.outcomeOf$(OutcomeOf.scala:31)
	at org.scalatest.OutcomeOf$.outcomeOf(OutcomeOf.scala:104)
	at org.scalatest.Transformer.apply(Transformer.scala:22)
	at org.scalatest.Transformer.apply(Transformer.scala:21)
	at org.scalatest.flatspec.AnyFlatSpecLike$$anon$5.apply(AnyFlatSpecLike.scala:1717)
	at org.scalatest.TestSuite.withFixture(TestSuite.scala:196)
	at org.scalatest.TestSuite.withFixture$(TestSuite.scala:138)
	at org.scalatest.flatspec.AnyFlatSpec.withFixture(AnyFlatSpec.scala:1685)
	at org.scalatest.flatspec.AnyFlatSpecLike.invokeWithFixture$1(AnyFlatSpecLike.scala:1723)
	at org.scalatest.flatspec.AnyFlatSpecLike.runTest$$anonfun$1(AnyFlatSpecLike.scala:1727)
	at org.scalatest.SuperEngine.runTestImpl(Engine.scala:306)
	at org.scalatest.flatspec.AnyFlatSpecLike.runTest(AnyFlatSpecLike.scala:1727)
	at org.scalatest.flatspec.AnyFlatSpecLike.runTest$(AnyFlatSpecLike.scala:51)
	at org.scalatest.flatspec.AnyFlatSpec.runTest(AnyFlatSpec.scala:1685)
	at org.scalatest.flatspec.AnyFlatSpecLike.runTests$$anonfun$1(AnyFlatSpecLike.scala:1785)
	at org.scalatest.SuperEngine.traverseSubNodes$1$$anonfun$1(Engine.scala:413)
	at scala.runtime.function.JProcedure1.apply(JProcedure1.java:15)
	at scala.runtime.function.JProcedure1.apply(JProcedure1.java:10)
	at scala.collection.immutable.List.foreach(List.scala:334)
	at org.scalatest.SuperEngine.traverseSubNodes$1(Engine.scala:429)
	at org.scalatest.SuperEngine.runTestsInBranch(Engine.scala:390)
	at org.scalatest.SuperEngine.traverseSubNodes$1$$anonfun$1(Engine.scala:427)
	at scala.runtime.function.JProcedure1.apply(JProcedure1.java:15)
	at scala.runtime.function.JProcedure1.apply(JProcedure1.java:10)
	at scala.collection.immutable.List.foreach(List.scala:334)
	at org.scalatest.SuperEngine.traverseSubNodes$1(Engine.scala:429)
	at org.scalatest.SuperEngine.runTestsInBranch(Engine.scala:396)
	at org.scalatest.SuperEngine.runTestsImpl(Engine.scala:475)
	at org.scalatest.flatspec.AnyFlatSpecLike.runTests(AnyFlatSpecLike.scala:1785)
	at org.scalatest.flatspec.AnyFlatSpecLike.runTests$(AnyFlatSpecLike.scala:51)
	at org.scalatest.flatspec.AnyFlatSpec.runTests(AnyFlatSpec.scala:1685)
	at org.scalatest.Suite.run(Suite.scala:1114)
	at org.scalatest.Suite.run$(Suite.scala:564)
	at org.scalatest.flatspec.AnyFlatSpec.org$scalatest$flatspec$AnyFlatSpecLike$$super$run(AnyFlatSpec.scala:1685)
	at org.scalatest.flatspec.AnyFlatSpecLike.run$$anonfun$1(AnyFlatSpecLike.scala:1830)
	at org.scalatest.SuperEngine.runImpl(Engine.scala:535)
	at org.scalatest.flatspec.AnyFlatSpecLike.run(AnyFlatSpecLike.scala:1830)
	at org.scalatest.flatspec.AnyFlatSpecLike.run$(AnyFlatSpecLike.scala:51)
	at org.scalatest.flatspec.AnyFlatSpec.run(AnyFlatSpec.scala:1685)
	at org.scalatest.tools.Framework.org$scalatest$tools$Framework$$runSuite(Framework.scala:321)
	at org.scalatest.tools.Framework$ScalaTestTask.execute(Framework.scala:517)
	at sbt.TestRunner.runTest$1(TestFramework.scala:153)
	at sbt.TestRunner.run(TestFramework.scala:168)
	at sbt.TestFramework$$anon$3$$anonfun$$lessinit$greater$1.$anonfun$apply$1(TestFramework.scala:336)
	at sbt.TestFramework$.sbt$TestFramework$$withContextLoader(TestFramework.scala:296)
	at sbt.TestFramework$$anon$3$$anonfun$$lessinit$greater$1.apply(TestFramework.scala:336)
	at sbt.TestFramework$$anon$3$$anonfun$$lessinit$greater$1.apply(TestFramework.scala:336)
	at sbt.TestFunction.apply(TestFramework.scala:348)
	at sbt.Tests$.$anonfun$toTask$1(Tests.scala:436)
	at sbt.std.Transform$$anon$3.$anonfun$apply$2(Transform.scala:47)
	at sbt.std.Transform$$anon$4.work(Transform.scala:69)
	at sbt.Execute.$anonfun$submit$2(Execute.scala:283)
	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:24)
	at sbt.Execute.work(Execute.scala:292)
	at sbt.Execute.$anonfun$submit$1(Execute.scala:283)
	at sbt.ConcurrentRestrictions$$anon$4.$anonfun$submitValid$1(ConcurrentRestrictions.scala:265)
	at sbt.CompletionService$$anon$2.call(CompletionService.scala:65)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
</failure>
                    </testcase><testcase classname="ModelSpec" name="FSM should handle edge cases" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Interpreter trait should allow for different implementations" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Interpreter trait should support stateful interpreters" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="Result trait should be implemented by both result types" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="Result trait should support pattern matching" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Integration should work end-to-end with multiple FSM instances" time="0.001">
                      
                    </testcase>
          <system-out><![CDATA[]]></system-out>
          <system-err><![CDATA[]]></system-err>
        </testsuite>