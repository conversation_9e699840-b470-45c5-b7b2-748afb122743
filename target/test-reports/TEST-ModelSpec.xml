<?xml version='1.0' encoding='UTF-8'?>
<testsuite hostname="Johnnies-MacBook-Pro-2.local" name="ModelSpec" tests="20" errors="0" failures="0" skipped="0" time="0.01" timestamp="2025-06-26T15:37:56">
          <properties>
      <property name="java.specification.version" value="23"/><property name="sun.jnu.encoding" value="UTF-8"/><property name="java.class.path" value="/opt/homebrew/Cellar/sbt/1.10.11/libexec/bin/sbt-launch.jar"/><property name="java.vm.vendor" value="Homebrew"/><property name="sun.arch.data.model" value="64"/><property name="jline.shutdownhook" value="false"/><property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/><property name="user.timezone" value="Europe/London"/><property name="java.vm.specification.version" value="23"/><property name="os.name" value="Mac OS X"/><property name="sun.java.launcher" value="SUN_STANDARD"/><property name="user.country" value="GB"/><property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/><property name="sun.java.command" value="/opt/homebrew/Cellar/sbt/1.10.11/libexec/bin/sbt-launch.jar test"/><property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/><property name="jdk.debug" value="release"/><property name="sun.cpu.endian" value="little"/><property name="user.home" value="/Users/<USER>"/><property name="user.language" value="en"/><property name="sbt.script" value="/opt/homebrew/Cellar/sbt/1.10.11/libexec/bin/sbt"/><property name="java.specification.vendor" value="Oracle Corporation"/><property name="sbt.ipcsocket.tmpdir" value="/var/folders/qj/std_nz6x3cb2vytf8lph0jw00000gn/T/.sbteee92d51/ipcsocket"/><property name="swoval.tmpdir" value="/var/folders/qj/std_nz6x3cb2vytf8lph0jw00000gn/T/.sbteee92d51/swoval"/><property name="java.version.date" value="2025-01-21"/><property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/><property name="file.separator" value="/"/><property name="java.vm.compressedOopsMode" value="Zero based"/><property name="line.separator" value="
"/><property name="java.vm.specification.vendor" value="Oracle Corporation"/><property name="java.specification.name" value="Java Platform API Specification"/><property name="apple.awt.application.name" value="Boot"/><property name="jline.esc.timeout" value="0"/><property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/><property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/><property name="java.runtime.version" value="23.0.2"/><property name="user.name" value="johnnie"/><property name="stdout.encoding" value="UTF-8"/><property name="path.separator" value=":"/><property name="os.version" value="15.5"/><property name="jna.nosys" value="true"/><property name="java.runtime.name" value="OpenJDK Runtime Environment"/><property name="log4j.ignoreTCL" value="true"/><property name="file.encoding" value="UTF-8"/><property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/><property name="java.vendor.version" value="Homebrew"/><property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/><property name="java.io.tmpdir" value="/var/folders/qj/std_nz6x3cb2vytf8lph0jw00000gn/T/"/><property name="java.version" value="23.0.2"/><property name="user.dir" value="/Users/<USER>/SourceCode/cats-state"/><property name="os.arch" value="aarch64"/><property name="java.vm.specification.name" value="Java Virtual Machine Specification"/><property name="native.encoding" value="UTF-8"/><property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/><property name="java.vm.info" value="mixed mode, sharing"/><property name="stderr.encoding" value="UTF-8"/><property name="java.vendor" value="Homebrew"/><property name="java.vm.version" value="23.0.2"/><property name="scala.ext.dirs" value="/Users/<USER>/.sbt/1.0/java9-rt-ext-homebrew_23_0_2"/><property name="sun.io.unicode.encoding" value="UnicodeBig"/><property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/><property name="java.class.version" value="67.0"/>
    </properties>
          <testcase classname="ModelSpec" name="Response should be created with string and int values" time="0.005">
                      
                    </testcase><testcase classname="ModelSpec" name="Response should support equality" time="0.002">
                      
                    </testcase><testcase classname="ModelSpec" name="Response should support copy method" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiOneResult should extend Result trait" time="0.001">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiOneResult should support equality" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiTwoResult should extend Result trait" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ApiTwoResult should support equality" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should implement op0 correctly" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should implement op1 correctly" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should return consistent results regardless of input" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="ConcreteInterpreter should handle empty string input" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should combine results from both operations" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should work with custom interpreter" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should handle different inputs with custom interpreter" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="FSM should handle edge cases" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Interpreter trait should allow for different implementations" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Interpreter trait should support stateful interpreters" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Result trait should be implemented by both result types" time="0.002">
                      
                    </testcase><testcase classname="ModelSpec" name="Result trait should support pattern matching" time="0.0">
                      
                    </testcase><testcase classname="ModelSpec" name="Integration should work end-to-end with multiple FSM instances" time="0.0">
                      
                    </testcase>
          <system-out><![CDATA[]]></system-out>
          <system-err><![CDATA[]]></system-err>
        </testsuite>