{"version": "1.4.0", "project": {"name": "root", "directory": "/Users/<USER>/SourceCode/cats-state", "workspaceDir": "/Users/<USER>/SourceCode/cats-state", "sources": ["/Users/<USER>/SourceCode/cats-state/src/main/scala", "/Users/<USER>/SourceCode/cats-state/src/main/scala-2.13", "/Users/<USER>/SourceCode/cats-state/src/main/scala-2", "/Users/<USER>/SourceCode/cats-state/src/main/java", "/Users/<USER>/SourceCode/cats-state/target/scala-2.13/src_managed/main"], "dependencies": [], "classpath": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0.jar"], "out": "/Users/<USER>/SourceCode/cats-state/.bloop/root", "classesDir": "/Users/<USER>/SourceCode/cats-state/.bloop/root/scala-2.13/classes", "resources": ["/Users/<USER>/SourceCode/cats-state/src/main/resources", "/Users/<USER>/SourceCode/cats-state/target/scala-2.13/resource_managed/main"], "scala": {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.13.12", "options": [], "jars": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.12/scala-compiler-2.13.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.12/scala-reflect-2.13.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.12/java-diff-utils-4.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline/3.22.0/jline-3.22.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar"], "setup": {"order": "mixed", "addLibraryToBootClasspath": true, "addCompilerToClasspath": false, "addExtraJarsToClasspath": false, "manageBootClasspath": true, "filterLibraryFromClasspath": true}, "bridgeJars": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala2-sbt-bridge/2.13.12/scala2-sbt-bridge-2.13.12.jar"]}, "java": {"options": []}, "test": {"frameworks": [{"names": ["org.scalacheck.ScalaCheckFramework"]}, {"names": ["org.specs2.runner.Specs2Framework", "org.specs2.runner.SpecsFramework"]}, {"names": ["org.specs.runner.SpecsFramework"]}, {"names": ["org.scalatest.tools.Framework", "org.scalatest.tools.ScalaTestFramework"]}, {"names": ["com.novocode.junit.JUnitFramework"]}, {"names": ["munit.Framework"]}, {"names": ["zio.test.sbt.ZTestFramework"]}, {"names": ["weaver.framework.CatsEffect"]}, {"names": ["hedgehog.sbt.Framework"]}], "options": {"excludes": [], "arguments": []}}, "platform": {"name": "jvm", "config": {"home": "/Users/<USER>/Library/Caches/Coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_aarch64_mac_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6/Contents/Home", "options": ["-Duser.dir=/Users/<USER>/SourceCode/cats-state"]}, "mainClass": [], "runtimeConfig": {"home": "/Users/<USER>/Library/Caches/Coursier/arc/https/github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.15%252B6/OpenJDK17U-jdk_aarch64_mac_hotspot_17.0.15_6.tar.gz/jdk-17.0.15+6/Contents/Home", "options": ["-Duser.dir=/Users/<USER>/SourceCode/cats-state"]}, "classpath": ["/Users/<USER>/SourceCode/cats-state/.bloop/root/scala-2.13/classes", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0.jar"]}, "resolution": {"modules": [{"organization": "org.scala-lang", "name": "scala-library", "version": "2.13.12", "configurations": "default", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-core_2.13", "version": "2.10.0", "configurations": "default", "artifacts": [{"name": "cats-core_2.13", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0.jar"}, {"name": "cats-core_2.13", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-core_2.13/2.10.0/cats-core_2.13-2.10.0-sources.jar"}]}, {"organization": "org.typelevel", "name": "cats-kernel_2.13", "version": "2.10.0", "configurations": "default", "artifacts": [{"name": "cats-kernel_2.13", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0.jar"}, {"name": "cats-kernel_2.13", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/typelevel/cats-kernel_2.13/2.10.0/cats-kernel_2.13-2.10.0-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.13.12", "configurations": "optional", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.12/scala-library-2.13.12-sources.jar"}]}]}, "tags": ["library"]}}