import cats.effect.{IO, Ref}
import cats.effect.std.MapRef
import cats.implicits._

// Domain models
case class Response(x: String, y: Int)

sealed trait Result
case class ApiOneResult(x: String) extends Result
case class ApiTwoResult(y: Int) extends Result

sealed trait Event
case class Begin(pid: String) extends Event
case class Foo(data: String) extends Event
case class Bar(data: Int) extends Event
object Done extends Event

enum State:
  case Start, Processing, Stopped

trait Interpreter:
  def op0(data: String): IO[ApiOneResult]
  def op1(data: String): IO[ApiTwoResult]

object ConcreteInterpreter extends Interpreter:
  def op0(data: String): IO[ApiOneResult] =
    IO.pure(ApiOneResult("Hello, World"))

  def op1(data: String): IO[ApiTwoResult] =
    IO.pure(ApiTwoResult(42))

type Store = MapRef[IO, String, Option[Result]]

class FSM(interpreter: Interpreter, store: Store, state: Ref[IO, State]):

  def run(event: Event): IO[Store] =
    state.get.flatMap { _state =>
      (_state, event) match
        case (State.Start, Begin(pid)) =>
          // Begin event triggers op0 and transitions to Processing
          for
            result <- interpreter.op0(pid)
            _ <- store("apiOne").set(Some(result))
            _ <- state.set(State.Processing)
            s <- run(Foo("user-data"))
          yield s

        case (State.Processing, Foo(data)) =>
          // Foo event triggers op0 while staying in Processing
          for
            result <- interpreter.op1(data)
            _ <- store("apiTwo").set(Some(result))
            s <- run(Done)
          yield s

        case (State.Processing,Done) =>
          // Bar event triggers op1 and transitions to Stopped
          state.set(State.Stopped).as(store)
          
        case (State.Stopped, _) =>
          // No operations allowed in Stopped state
          IO.pure(store)

        case _ =>
          IO.raiseError(new RuntimeException(s"Invalid event $event for current state $state"))
    }

  // Get current state
  def getCurrentState: IO[State] = state.get

object FSM:
  def of(interpreter: Interpreter): IO[FSM] =
    for {
      store <- MapRef.ofSingleImmutableMap[IO, String, Result]()
      state <- Ref.of[IO, State](State.Start)
    } yield FSM(interpreter, store, state)
