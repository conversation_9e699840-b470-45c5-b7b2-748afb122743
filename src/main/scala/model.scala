import cats.effect.IO
import cats.implicits._

// Domain models
case class Response(x: String, y: Int)

sealed trait Result
case class ApiOneResult(x: String) extends Result
case class ApiTwoResult(y: Int) extends Result

trait Interpreter:
  def op0(data: String): IO[ApiOneResult]
  def op1(data: String): IO[ApiTwoResult]

object ConcreteInterpreter extends Interpreter:
  def op0(data: String): IO[ApiOneResult] =
    IO.pure(ApiOneResult("Hello, World"))

  def op1(data: String): IO[ApiTwoResult] =
    IO.pure(ApiTwoResult(42))

class FSM(interpreter: Interpreter):
  def run(data: String): IO[Response] =
    for
      result0 <- interpreter.op0(data)
      result1 <- interpreter.op1(data)
    yield Response(result0.x, result1.y)
