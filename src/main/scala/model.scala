
import cats.Monoid
import cats.implicits._

case class Response(foo: Option[Event.Foo] = None, bar: Option[Event.Bar] = None)

enum Event:
    case Foo(x: String)
    case Bar(y: Int)

// Monoid instances for Event types
given Monoid[Event.Foo] with
  def empty: Event.Foo = Event.Foo("")
  def combine(x: Event.Foo, y: Event.Foo): Event.Foo = Event.Foo(x.x + y.x)

given Monoid[Event.Bar] with
  def empty: Event.Bar = Event.Bar(0)
  def combine(x: Event.Bar, y: Event.Bar): Event.Bar = Event.Bar(x.y + y.y)

// Monoid instance for Response
given Monoid[Response] with
  def empty: Response = Response()

  def combine(x: Response, y: Response): Response =
    Response(
      x.foo |+| y.foo,  // Using cats combine operator for Option[Event.Foo]
      x.bar |+| y.bar   // Using cats combine operator for Option[Event.Bar]
    )
