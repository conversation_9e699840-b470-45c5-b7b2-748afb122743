import cats.data.EitherT
import cats.effect.{IO, Ref}
import cats.implicits._
import io.circe.Encoder
import io.circe.Json

// Domain models
case class Response(x: String, y: Int)

sealed trait Result
object Result:
  given Encoder[Result] = Encoder.instance {
    case ApiOneResult(x) => Json.obj("x" -> Json.fromString(x))
    case ApiTwoResult(y) => Json.obj("y" -> Json.fromInt(y))
  }
case class ApiOneResult(x: String) extends Result derives Encoder
case class ApiTwoResult(y: Int) extends Result derives Encoder

trait Interpreter:
  def op0(data: String): IO[ApiOneResult]
  def op1(data: String): IO[ApiTwoResult]

object ConcreteInterpreter extends Interpreter:
  def op0(data: String): IO[ApiOneResult] =
    IO.pure(ApiOneResult("Hello, World"))

  def op1(data: String): IO[ApiTwoResult] =
    IO.pure(ApiTwoResult(42))

// Add a more comprehensive result type
sealed trait ApiResult
case class ApiSuccess[T <: Result](result: T) extends ApiResult
case class ApiFailure(error: Throwable) extends ApiResult

class EntityAggregator(interpreter: Interpreter, store: Ref[IO, Map[String, ApiResult]]):
  
  // Safe operation that captures failures but continues processing
  private def safeOp[T <: Result](key: String, operation: IO[T]): IO[Unit] =
    operation
      .map(result => ApiSuccess(result))
      .handleError(error => ApiFailure(error))
      .flatMap(apiResult => store.update(_ + (key -> apiResult)))
  
  // Process all operations for an entity, continuing on failures
  def processEntity(entityId: String): IO[Map[String, ApiResult]] =
    for {
      // Initialize processing
      _ <- store.set(Map.empty)
      
      // Make API calls, capturing results but continuing on failure
      _ <- safeOp[ApiOneResult]("apiOne", interpreter.op0(entityId))
      _ <- safeOp[ApiTwoResult]("apiTwo", interpreter.op1(s"data-for-$entityId"))
      
      // Add more API calls as needed
      // _ <- safeOp("apiThree", interpreter.op2(...))
      
      // Return the complete result map
      results <- store.get
    } yield results
    
  // Helper to check if all operations succeeded
  def allSucceeded: IO[Boolean] =
    store.get.map(_.values.forall(_.isInstanceOf[ApiSuccess[_]]))
    
  // Helper to get successful results only
  def getSuccessfulResults: IO[Map[String, Result]] =
    store.get.map(_.collect {
      case (key, ApiSuccess(result)) => key -> result
    })

object EntityAggregator:
  def create(interpreter: Interpreter): IO[EntityAggregator] =
    Ref.of[IO, Map[String, ApiResult]](Map.empty).map(store => 
      new EntityAggregator(interpreter, store)
    )
