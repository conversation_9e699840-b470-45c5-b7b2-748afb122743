
import cats.Monoid
import cats.implicits._

case class Response(foo: Event, bar: Event)

enum Event:
    case Start
    case Foo(x: String)
    case Bar(y: Int)

// Monoid instance for Event
given Monoid[Event] with
  def empty: Event = Event.Start

  def combine(x: Event, y: Event): Event = (x, y) match
    case (Event.Start, other) => other
    case (other, Event.Start) => other
    case (Event.Foo(a), Event.Foo(b)) => Event.Foo(a + b)
    case (Event.Bar(a), Event.Bar(b)) => Event.Bar(a + b)
    case (Event.Foo(s), Event.Bar(_)) => Event.Foo(s) // Foo takes precedence
    case (Event.Bar(_), Event.Foo(s)) => Event.Foo(s) // Foo takes precedence

// Monoid instance for Response
given Monoid[Response] with
  def empty: Response = Response(Event.Start, Event.Start)

  def combine(x: Response, y: Response): Response =
    Response(
      x.foo |+| y.foo,  // Using cats combine operator
      x.bar |+| y.bar
    )
