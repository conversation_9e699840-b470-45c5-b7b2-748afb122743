
// Domain models
case class Response(x: String, y: Int)

sealed trait Result
case class ApiOneResult(x: String) extends Result
case class ApiTwoResult(y: Int) extends Result

trait Interpreter:
    def op0(data: String): ApiOneResult
    def op1(data: String): ApiTwoResult


object ConcreteInterpreter extends Interpreter:
    def op0(data: String): ApiOneResult = ApiOneResult("Hello, World")
    def op1(data: String): ApiTwoResult = ApiTwoResult(42)

class FSM(interpreter: Interpreter):
    def run(data: String): Response = {
        val result0 = interpreter.op0(data)
        val result1 = interpreter.op1(data)
        Response(result0.x, result1.y)
    }
