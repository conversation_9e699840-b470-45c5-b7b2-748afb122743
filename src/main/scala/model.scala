import cats.effect.IO
import cats.implicits._

// Domain models
case class Response(x: String, y: Int)

sealed trait Result
case class ApiOneResult(x: String) extends Result
case class ApiTwoResult(y: Int) extends Result

sealed trait Event
case class Begin(pid: String) extends Event
case class Foo(data: String) extends Event
case class Bar(data: Int) extends Event

enum State:
  case Start, Processing, Stopped

trait Interpreter:
  def op0(data: String): IO[ApiOneResult]
  def op1(data: String): IO[ApiTwoResult]

object ConcreteInterpreter extends Interpreter:
  def op0(data: String): IO[ApiOneResult] =
    IO.pure(ApiOneResult("Hello, World"))

  def op1(data: String): IO[ApiTwoResult] =
    IO.pure(ApiTwoResult(42))

class FSM(interpreter: Interpreter):
  private var state: State = State.Start
 
  def run(event: Event): IO[Response] = 
    (state, event) match
    case Start(pid) =>
      // Start event triggers op0 with the pid
      interpreter.op0(pid).map(result => Response(result.x, 0))

    case Event.Foo(data) =>
      // Foo event triggers op0 with the data
      interpreter.op0(data).map(result => Response(result.x, 0))

    case Event.Bar(data) =>
      // Bar event triggers op1 with the data converted to string
      interpreter.op1(data.toString).map(result => Response("", result.y))


  // Process multiple events sequentially, accumulating the results
  def runSequence(events: List[Event]): IO[Response] = {
    events.foldM(Response("", 0)) { (acc, event) =>
      run(event).map { newResp =>
        Response(
          if (newResp.x.nonEmpty) acc.x + newResp.x else acc.x,
          acc.y + newResp.y
        )
      }
    }
  }

  // Process multiple events in parallel and combine results
  def runParallel(events: List[Event]): IO[Response] = {
    events.traverse(run).map { responses =>
      responses.foldLeft(Response("", 0)) { (acc, resp) =>
        Response(
          if (resp.x.nonEmpty) acc.x + resp.x else acc.x,
          acc.y + resp.y
        )
      }
    }
  }
