import cats.effect.{IO, IOApp}
import cats.implicits._
import scala.concurrent.duration._

object Main extends IOApp.Simple {
  
  // Example of a simple effectful interpreter
  val simpleInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] = 
      IO.println(s"Processing op0 with: $data") *>
      IO.pure(ApiOneResult(s"Processed: $data"))
    
    def op1(data: String): IO[ApiTwoResult] = 
      IO.println(s"Processing op1 with: $data") *>
      IO.pure(ApiTwoResult(data.length))
  }
  
  // Example of an interpreter that simulates async operations
  val asyncInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] = for {
      _ <- IO.println(s"Starting async op0 for: $data")
      _ <- IO.sleep(100.millis) // Simulate network delay
      result = ApiOneResult(s"Async result: ${data.toUpperCase}")
      _ <- IO.println(s"Completed op0: $result")
    } yield result
    
    def op1(data: String): IO[ApiTwoResult] = for {
      _ <- IO.println(s"Starting async op1 for: $data")
      _ <- IO.sleep(50.millis) // Simulate network delay
      result = ApiTwoResult(data.hashCode.abs)
      _ <- IO.println(s"Completed op1: $result")
    } yield result
  }
  
  // Example of an interpreter that can fail
  val failableInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] = 
      if (data.nonEmpty) 
        IO.pure(ApiOneResult(s"Success: $data"))
      else 
        IO.raiseError(new RuntimeException("Empty data not allowed"))
    
    def op1(data: String): IO[ApiTwoResult] = 
      if (data.length <= 10) 
        IO.pure(ApiTwoResult(data.length))
      else 
        IO.raiseError(new RuntimeException("Data too long"))
  }
  
  def run: IO[Unit] = for {
    _ <- IO.println("=== FSM with Effectful Operations ===\n")
    
    // Example 1: Simple effectful operations
    _ <- IO.println("1. Simple effectful operations:")
    fsm1 = FSM(simpleInterpreter)
    response1 <- fsm1.run("hello world")
    _ <- IO.println(s"Response: $response1\n")
    
    // Example 2: Async operations
    _ <- IO.println("2. Async operations:")
    fsm2 = FSM(asyncInterpreter)
    response2 <- fsm2.run("async test")
    _ <- IO.println(s"Response: $response2\n")
    
    // Example 3: Parallel execution of multiple FSMs
    _ <- IO.println("3. Parallel execution:")
    fsm3 = FSM(ConcreteInterpreter)
    fsm4 = FSM(asyncInterpreter)
    (resp3, resp4) <- (fsm3.run("parallel1"), fsm4.run("parallel2")).parTupled
    _ <- IO.println(s"Parallel responses: $resp3, $resp4\n")
    
    // Example 4: Error handling
    _ <- IO.println("4. Error handling:")
    fsmFailable = FSM(failableInterpreter)
    _ <- fsmFailable.run("good").attempt.flatMap {
      case Right(response) => IO.println(s"Success: $response")
      case Left(error) => IO.println(s"Error: ${error.getMessage}")
    }
    _ <- fsmFailable.run("").attempt.flatMap {
      case Right(response) => IO.println(s"Success: $response")
      case Left(error) => IO.println(s"Error: ${error.getMessage}")
    }
    _ <- fsmFailable.run("this is too long").attempt.flatMap {
      case Right(response) => IO.println(s"Success: $response")
      case Left(error) => IO.println(s"Error: ${error.getMessage}")
    }
    
    _ <- IO.println("\n5. Sequential processing of multiple inputs:")
    inputs = List("first", "second", "third")
    responses <- inputs.traverse(fsm2.run)
    _ <- responses.zipWithIndex.traverse { case (resp, idx) =>
      IO.println(s"Input ${idx + 1}: $resp")
    }
    
  } yield ()
}
