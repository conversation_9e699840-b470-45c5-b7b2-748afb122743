import cats.effect.{IO, IOApp}
import cats.implicits._
import scala.concurrent.duration._

object Main extends IOApp.Simple {
  
  // Example of a simple effectful interpreter
  val simpleInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] =
      IO.println(s"Processing op0 with: $data") *>
      IO.pure(ApiOneResult(s"Processed: $data"))

    def op1(data: String): IO[ApiTwoResult] =
      IO.println(s"Processing op1 with: $data") *>
      IO.pure(ApiTwoResult(data.length))
  }
  
  // Example of an interpreter that simulates async operations
  val asyncInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] = for {
      _ <- IO.println(s"Starting async op0 for: $data")
      _ <- IO.sleep(100.millis) // Simulate network delay
      result = ApiOneResult(s"Async result: ${data.toUpperCase}")
      _ <- IO.println(s"Completed op0: $result")
    } yield result
    
    def op1(data: String): IO[ApiTwoResult] = for {
      _ <- IO.println(s"Starting async op1 for: $data")
      _ <- IO.sleep(50.millis) // Simulate network delay
      result = ApiTwoResult(data.hashCode.abs)
      _ <- IO.println(s"Completed op1: $result")
    } yield result
  }
  
  // Example of an interpreter that can fail
  val failableInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] = 
      if (data.nonEmpty) 
        IO.pure(ApiOneResult(s"Success: $data"))
      else 
        IO.raiseError(new RuntimeException("Empty data not allowed"))
    
    def op1(data: String): IO[ApiTwoResult] = 
      if (data.length <= 10) 
        IO.pure(ApiTwoResult(data.length))
      else 
        IO.raiseError(new RuntimeException("Data too long"))
  }
  
  def run: IO[Unit] = for {
    _ <- IO.println("=== Event-Driven State Machine ===\n")

    // Example 1: Different event types
    _ <- IO.println("1. Processing different event types:")
    fsm1 = FSM(simpleInterpreter)
    startResp <- fsm1.run(Event.Start("process-123"))
    _ <- IO.println(s"Start event response: $startResp")

    fooResp <- fsm1.run(Event.Foo("user-data"))
    _ <- IO.println(s"Foo event response: $fooResp")

    barResp <- fsm1.run(Event.Bar(42))
    _ <- IO.println(s"Bar event response: $barResp\n")

    // Example 2: Sequential event processing
    _ <- IO.println("2. Sequential event processing:")
    fsm2 = FSM(asyncInterpreter)
    events = List(
      Event.Start("session-456"),
      Event.Foo("config-data"),
      Event.Bar(100),
      Event.Bar(200)
    )
    seqResponse <- fsm2.runSequence(events)
    _ <- IO.println(s"Sequential processing result: $seqResponse\n")

    // Example 3: Parallel event processing
    _ <- IO.println("3. Parallel event processing:")
    parResponse <- fsm2.runParallel(events)
    _ <- IO.println(s"Parallel processing result: $parResponse\n")

    // Example 4: Error handling with events
    _ <- IO.println("4. Error handling:")
    fsmFailable = FSM(failableInterpreter)
    _ <- fsmFailable.run(Event.Start("good")).attempt.flatMap {
      case Right(response) => IO.println(s"Success: $response")
      case Left(error) => IO.println(s"Error: ${error.getMessage}")
    }
    _ <- fsmFailable.run(Event.Start("")).attempt.flatMap {
      case Right(response) => IO.println(s"Success: $response")
      case Left(error) => IO.println(s"Error: ${error.getMessage}")
    }
    _ <- fsmFailable.run(Event.Bar(12345678901L.toInt)).attempt.flatMap {
      case Right(response) => IO.println(s"Success: $response")
      case Left(error) => IO.println(s"Error: ${error.getMessage}")
    }

    _ <- IO.println("\n5. Event stream processing:")
    eventStream = List(
      Event.Start("stream-1"),
      Event.Foo("data-1"),
      Event.Bar(10),
      Event.Start("stream-2"),
      Event.Foo("data-2"),
      Event.Bar(20)
    )
    responses <- eventStream.traverse(fsm1.run)
    _ <- responses.zipWithIndex.traverse { case (resp, idx) =>
      IO.println(s"Event ${idx + 1}: $resp")
    }

  } yield ()
}
