import cats.effect.{IO, IOApp}
import cats.implicits.*
import scala.concurrent.duration._
import cats.effect.std.MapRef

object Main extends IOApp.Simple:

  case class Accumulator(apiOne: Option[Result], apiTwo: Option[Result])
  
  // Example of a simple effectful interpreter
  val simpleInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] =
      IO.println(s"Processing op0 with: $data") *>
      IO.pure(ApiOneResult(s"Processed: $data"))

    def op1(data: String): IO[ApiTwoResult] =
      IO.println(s"Processing op1 with: $data") *>
      IO.pure(ApiTwoResult(data.length))
  }
  
  def run: IO[Unit] = 
    val acc = for
      _ <- IO.println("=== Stateful Event-Driven FSM ===\n")
      fsm <- FSM.of(simpleInterpreter)
      _ <- IO.println(s"Initial state: ${fsm.getCurrentState}")
      store <- fsm.run(Begin("process-123"))
      a <- store("apiOne").get
      b <- store("apiTwo").get
    yield Accumulator(a, b)
  
    acc.flatMap { acc =>
      IO.println(s"Accumulated results: $acc")
    }
  
  