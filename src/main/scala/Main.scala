import cats.implicits._
import cats.data.State

object Main extends App {
  // Example using cats State monad
  type Stack = List[Int]
  
  def push(x: Int): State[Stack, Unit] = State.modify(x :: _)
  
  def pop: State[Stack, Int] = State { stack =>
    stack match {
      case Nil => throw new RuntimeException("Stack is empty")
      case head :: tail => (tail, head)
    }
  }
  
  def peek: State[Stack, Int] = State.inspect(_.headOption.getOrElse(0))
  
  // Example computation using State
  val computation = for {
    _ <- push(1)
    _ <- push(2)
    _ <- push(3)
    top <- peek
    _ <- pop
    second <- peek
  } yield (top, second)
  
  val initialStack: Stack = List.empty
  val (finalStack, result) = computation.run(initialStack).value
  
  println(s"Result: $result")
  println(s"Final stack: $finalStack")
  
  // Example using cats Option syntax
  val maybeValue = Option(42)
  val doubled = maybeValue.map(_ * 2)
  println(s"Doubled value: $doubled")
  
  // Example using cats Either syntax
  val rightValue: Either[String, Int] = 42.asRight
  val leftValue: Either[String, Int] = "Error".asLeft
  
  println(s"Right value: $rightValue")
  println(s"Left value: $leftValue")

  // Example using Monoid instances for Event and Response
  println("\n--- Monoid Examples ---")

  val event1 = Event.Foo("Hello")
  val event2 = Event.Foo(" World")
  val combinedEvent = event1 |+| event2
  println(s"Combined events: $combinedEvent")

  val response1 = Response(Some(Event.Foo("Scala")), Some(Event.Bar(3)))
  val response2 = Response(Some(Event.Foo(" is awesome")), Some(Event.Bar(39)))
  val combinedResponse = response1 |+| response2
  println(s"Combined responses: $combinedResponse")

  // Using combineAll with a list
  val responses = List(
    Response(Some(Event.Foo("Cats")), Some(Event.Bar(1))),
    Response(Some(Event.Foo(" makes")), Some(Event.Bar(2))),
    Response(Some(Event.Foo(" FP easy")), Some(Event.Bar(3)))
  )
  val allCombined = responses.combineAll
  println(s"All responses combined: $allCombined")

  // Example with None values
  val partialResponse1 = Response(Some(Event.Foo("Only foo")), None)
  val partialResponse2 = Response(None, Some(Event.Bar(42)))
  val combinedPartial = partialResponse1 |+| partialResponse2
  println(s"Combined partial responses: $combinedPartial")
}
