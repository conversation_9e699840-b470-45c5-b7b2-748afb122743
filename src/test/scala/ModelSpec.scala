import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import cats.effect.IO
import cats.effect.unsafe.implicits.global

class ModelSpec extends AnyFlatSpec with Matchers {
  
  "Response" should "be created with string and int values" in {
    val response = Response("test", 42)
    response.x shouldBe "test"
    response.y shouldBe 42
  }
  
  it should "support equality" in {
    val response1 = Response("hello", 123)
    val response2 = Response("hello", 123)
    val response3 = Response("world", 456)
    
    response1 shouldBe response2
    response1 should not be response3
  }
  
  it should "support copy method" in {
    val original = Response("original", 100)
    val modified = original.copy(x = "modified")
    
    modified shouldBe Response("modified", 100)
    original shouldBe Response("original", 100) // original unchanged
  }
  
  "ApiOneResult" should "extend Result trait" in {
    val result = ApiOneResult("test data")
    result shouldBe a[Result]
    result.x shouldBe "test data"
  }
  
  it should "support equality" in {
    val result1 = ApiOneResult("same")
    val result2 = ApiOneResult("same")
    val result3 = ApiOneResult("different")
    
    result1 shouldBe result2
    result1 should not be result3
  }
  
  "ApiTwoResult" should "extend Result trait" in {
    val result = ApiTwoResult(100)
    result shouldBe a[Result]
    result.y shouldBe 100
  }
  
  it should "support equality" in {
    val result1 = ApiTwoResult(42)
    val result2 = ApiTwoResult(42)
    val result3 = ApiTwoResult(99)
    
    result1 shouldBe result2
    result1 should not be result3
  }
  
  "ConcreteInterpreter" should "implement op0 correctly" in {
    val result = ConcreteInterpreter.op0("any input").unsafeRunSync()
    result shouldBe ApiOneResult("Hello, World")
  }

  it should "implement op1 correctly" in {
    val result = ConcreteInterpreter.op1("any input").unsafeRunSync()
    result shouldBe ApiTwoResult(42)
  }

  it should "return consistent results regardless of input" in {
    val result1 = ConcreteInterpreter.op0("input1").unsafeRunSync()
    val result2 = ConcreteInterpreter.op0("input2").unsafeRunSync()
    val result3 = ConcreteInterpreter.op1("input1").unsafeRunSync()
    val result4 = ConcreteInterpreter.op1("input2").unsafeRunSync()

    result1 shouldBe result2
    result3 shouldBe result4
  }

  it should "handle empty string input" in {
    val result1 = ConcreteInterpreter.op0("").unsafeRunSync()
    val result2 = ConcreteInterpreter.op1("").unsafeRunSync()

    result1 shouldBe ApiOneResult("Hello, World")
    result2 shouldBe ApiTwoResult(42)
  }
  
  "FSM" should "combine results from both operations" in {
    val fsm = FSM(ConcreteInterpreter)
    val response = fsm.run("test input").unsafeRunSync()

    response shouldBe Response("Hello, World", 42)
  }

  it should "work with custom interpreter" in {
    val customInterpreter = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(s"Processed: $data"))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(data.length))
    }

    val fsm = FSM(customInterpreter)
    val response = fsm.run("test").unsafeRunSync()

    response shouldBe Response("Processed: test", 4)
  }

  it should "handle different inputs with custom interpreter" in {
    val customInterpreter = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(data.toUpperCase))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(data.length * 10))
    }

    val fsm = FSM(customInterpreter)

    val response1 = fsm.run("hello").unsafeRunSync()
    val response2 = fsm.run("world!").unsafeRunSync()

    response1 shouldBe Response("HELLO", 50)
    response2 shouldBe Response("WORLD!", 60)
  }
  
  it should "handle edge cases" in {
    val edgeCaseInterpreter = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(
        if (data.isEmpty) "empty" else data
      ))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(
        if (data.isEmpty) -1 else data.hashCode
      ))
    }

    val fsm = FSM(edgeCaseInterpreter)

    val emptyResponse = fsm.run("").unsafeRunSync()
    val normalResponse = fsm.run("test").unsafeRunSync()

    emptyResponse.x shouldBe "empty"
    emptyResponse.y shouldBe -1
    normalResponse.x shouldBe "test"
    normalResponse.y shouldBe "test".hashCode
  }
  
  "Interpreter trait" should "allow for different implementations" in {
    val interpreter1 = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult("impl1"))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(1))
    }

    val interpreter2 = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult("impl2"))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(2))
    }

    val fsm1 = FSM(interpreter1)
    val fsm2 = FSM(interpreter2)

    fsm1.run("test").unsafeRunSync() shouldBe Response("impl1", 1)
    fsm2.run("test").unsafeRunSync() shouldBe Response("impl2", 2)
  }

  it should "support stateful interpreters" in {
    var counter = 0
    val statefulInterpreter = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO {
        counter += 1
        ApiOneResult(s"call-$counter")
      }
      def op1(data: String): IO[ApiTwoResult] = IO {
        counter += 10
        ApiTwoResult(counter)
      }
    }

    val fsm = FSM(statefulInterpreter)

    val response1 = fsm.run("first").unsafeRunSync()
    val response2 = fsm.run("second").unsafeRunSync()

    response1 shouldBe Response("call-1", 11)
    response2 shouldBe Response("call-12", 22)
  }
  
  "Result trait" should "be implemented by both result types" in {
    val results: List[Result] = List(
      ApiOneResult("test"),
      ApiTwoResult(123)
    )
    
    results should have size 2
    results(0) shouldBe a[ApiOneResult]
    results(1) shouldBe a[ApiTwoResult]
  }
  
  it should "support pattern matching" in {
    val results: List[Result] = List(
      ApiOneResult("hello"),
      ApiTwoResult(42),
      ApiOneResult("world")
    )
    
    val strings = results.collect {
      case ApiOneResult(x) => x
    }
    
    val numbers = results.collect {
      case ApiTwoResult(y) => y
    }
    
    strings shouldBe List("hello", "world")
    numbers shouldBe List(42)
  }
  
  "Integration" should "work end-to-end with multiple FSM instances" in {
    val fsm1 = FSM(ConcreteInterpreter)
    val fsm2 = FSM(new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(s"Custom: $data"))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(data.length * 2))
    })

    val response1 = fsm1.run("test").unsafeRunSync()
    val response2 = fsm2.run("test").unsafeRunSync()

    response1 shouldBe Response("Hello, World", 42)
    response2 shouldBe Response("Custom: test", 8)
  }
}
