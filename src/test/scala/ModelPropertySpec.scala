import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks
import org.scalacheck.Gen
import cats.effect.IO
import cats.effect.unsafe.implicits.global

class ModelPropertySpec extends AnyFlatSpec with Matchers with ScalaCheckPropertyChecks {
  
  "Response" should "maintain its properties under copy operations" in {
    forAll { (x: String, y: Int, newX: String, newY: Int) =>
      val original = Response(x, y)
      val copiedX = original.copy(x = newX)
      val copiedY = original.copy(y = newY)
      val copiedBoth = original.copy(x = newX, y = newY)
      
      copiedX.x shouldBe newX
      copiedX.y shouldBe y
      
      copiedY.x shouldBe x
      copiedY.y shouldBe newY
      
      copiedBoth.x shouldBe newX
      copiedBoth.y shouldBe newY
      
      // Original should be unchanged
      original.x shouldBe x
      original.y shouldBe y
    }
  }
  
  "Response equality" should "be reflexive, symmetric, and transitive" in {
    forAll { (x: String, y: Int) =>
      val response = Response(x, y)
      
      // Reflexive
      response shouldBe response
      
      // Symmetric
      val duplicate = Response(x, y)
      response shouldBe duplicate
      duplicate shouldBe response
      
      // Transitive (with a third identical instance)
      val another = Response(x, y)
      if (response == duplicate && duplicate == another) {
        response shouldBe another
      }
    }
  }
  
  "ApiOneResult" should "preserve input data" in {
    forAll { (input: String) =>
      val result = ApiOneResult(input)
      result.x shouldBe input
      result shouldBe a[Result]
    }
  }
  
  "ApiTwoResult" should "preserve input data" in {
    forAll { (input: Int) =>
      val result = ApiTwoResult(input)
      result.y shouldBe input
      result shouldBe a[Result]
    }
  }
  
  "ConcreteInterpreter" should "always return the same values" in {
    forAll { (input: String) =>
      val result1 = ConcreteInterpreter.op0(input).unsafeRunSync()
      val result2 = ConcreteInterpreter.op0(input).unsafeRunSync()
      val result3 = ConcreteInterpreter.op1(input).unsafeRunSync()
      val result4 = ConcreteInterpreter.op1(input).unsafeRunSync()

      result1 shouldBe result2
      result3 shouldBe result4

      result1 shouldBe ApiOneResult("Hello, World")
      result3 shouldBe ApiTwoResult(42)
    }
  }

  "FSM with ConcreteInterpreter" should "always produce the same response" in {
    forAll { (input: String) =>
      val fsm = FSM(ConcreteInterpreter)
      val response = fsm.run(input).unsafeRunSync()

      response shouldBe Response("Hello, World", 42)
    }
  }

  "FSM with custom interpreter" should "correctly transform inputs" in {
    val lengthInterpreter = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(s"Length: ${data.length}"))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(data.length))
    }

    forAll { (input: String) =>
      val fsm = FSM(lengthInterpreter)
      val response = fsm.run(input).unsafeRunSync()

      response.x shouldBe s"Length: ${input.length}"
      response.y shouldBe input.length
    }
  }
  
  "FSM with transformation interpreter" should "handle various string transformations" in {
    val transformInterpreter = new Interpreter {
      def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(data.reverse))
      def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(data.count(_.isDigit)))
    }

    forAll { (input: String) =>
      val fsm = FSM(transformInterpreter)
      val response = fsm.run(input).unsafeRunSync()

      response.x shouldBe input.reverse
      response.y shouldBe input.count(_.isDigit)
    }
  }
  
  "Result pattern matching" should "work correctly for all instances" in {
    val stringGen = Gen.alphaNumStr
    val intGen = Gen.choose(Int.MinValue, Int.MaxValue)
    
    forAll(stringGen, intGen) { (str: String, num: Int) =>
      val results: List[Result] = List(
        ApiOneResult(str),
        ApiTwoResult(num)
      )
      
      val extractedStrings = results.collect { case ApiOneResult(x) => x }
      val extractedInts = results.collect { case ApiTwoResult(y) => y }
      
      extractedStrings should contain(str)
      extractedInts should contain(num)
    }
  }
  
  "FSM composition" should "work with multiple interpreters" in {
    forAll { (input: String) =>
      val interpreter1 = new Interpreter {
        def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(s"First: $data"))
        def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(1))
      }

      val interpreter2 = new Interpreter {
        def op0(data: String): IO[ApiOneResult] = IO.pure(ApiOneResult(s"Second: $data"))
        def op1(data: String): IO[ApiTwoResult] = IO.pure(ApiTwoResult(2))
      }

      val fsm1 = FSM(interpreter1)
      val fsm2 = FSM(interpreter2)

      val response1 = fsm1.run(input).unsafeRunSync()
      val response2 = fsm2.run(input).unsafeRunSync()

      response1.x shouldBe s"First: $input"
      response1.y shouldBe 1

      response2.x shouldBe s"Second: $input"
      response2.y shouldBe 2

      response1 should not be response2
    }
  }
}
