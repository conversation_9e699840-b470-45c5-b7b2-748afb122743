import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import cats.implicits._
import cats.data.State

class MainSpec extends AnyFlatSpec with Matchers {
  
  "Stack operations" should "work correctly with State monad" in {
    type Stack = List[Int]
    
    def push(x: Int): State[Stack, Unit] = State.modify(x :: _)
    def pop: State[Stack, Int] = State { stack =>
      stack match {
        case Nil => throw new RuntimeException("Stack is empty")
        case head :: tail => (tail, head)
      }
    }
    def peek: State[Stack, Int] = State.inspect(_.headOption.getOrElse(0))
    
    val computation = for {
      _ <- push(10)
      _ <- push(20)
      top <- peek
      _ <- pop
      second <- peek
    } yield (top, second)
    
    val initialStack: Stack = List.empty
    val (finalStack, result) = computation.run(initialStack).value
    
    result shouldBe (20, 10)
    finalStack shouldBe List(10)
  }
  
  "Cats syntax" should "work with Op<PERSON>" in {
    val maybeValue = Option(42)
    val doubled = maybeValue.map(_ * 2)
    
    doubled shouldBe Some(84)
  }
  
  "Cats syntax" should "work with Either" in {
    val rightValue: Either[String, Int] = 42.asRight
    val leftValue: Either[String, Int] = "Error".asLeft
    
    rightValue shouldBe Right(42)
    leftValue shouldBe Left("Error")
  }
}
