import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import cats.implicits._

class MonoidSpec extends AnyFlatSpec with Matchers {
  
  "Event Monoid" should "have Start as empty element" in {
    import cats.Monoid
    val empty = Monoid[Event].empty
    empty shouldBe Event.Start
  }
  
  it should "combine with Start as identity" in {
    val foo = Event.Foo("hello")
    val bar = Event.Bar(42)
    
    foo |+| Event.Start shouldBe foo
    Event.Start |+| foo shouldBe foo
    bar |+| Event.Start shouldBe bar
    Event.Start |+| bar shouldBe bar
  }
  
  it should "combine Foo events by concatenating strings" in {
    val foo1 = Event.Foo("hello")
    val foo2 = Event.Foo(" world")
    
    foo1 |+| foo2 shouldBe Event.Foo("hello world")
  }
  
  it should "combine Bar events by adding integers" in {
    val bar1 = Event.Bar(10)
    val bar2 = Event.Bar(32)
    
    bar1 |+| bar2 shouldBe Event.Bar(42)
  }
  
  it should "give precedence to Foo when combining Foo and Bar" in {
    val foo = Event.Foo("text")
    val bar = Event.Bar(123)
    
    foo |+| bar shouldBe Event.Foo("text")
    bar |+| foo shouldBe Event.Foo("text")
  }
  
  it should "be associative" in {
    val a = Event.Foo("a")
    val b = Event.Foo("b")
    val c = Event.Foo("c")
    
    (a |+| b) |+| c shouldBe a |+| (b |+| c)
    
    val x = Event.Bar(1)
    val y = Event.Bar(2)
    val z = Event.Bar(3)
    
    (x |+| y) |+| z shouldBe x |+| (y |+| z)
  }
  
  "Response Monoid" should "have empty Response with Start events" in {
    import cats.Monoid
    val empty = Monoid[Response].empty
    empty shouldBe Response(Event.Start, Event.Start)
  }
  
  it should "combine Responses by combining their fields" in {
    val resp1 = Response(Event.Foo("hello"), Event.Bar(10))
    val resp2 = Response(Event.Foo(" world"), Event.Bar(32))
    
    val combined = resp1 |+| resp2
    combined shouldBe Response(Event.Foo("hello world"), Event.Bar(42))
  }
  
  it should "work with empty as identity" in {
    import cats.Monoid
    val resp = Response(Event.Foo("test"), Event.Bar(100))
    val empty = Monoid[Response].empty
    
    resp |+| empty shouldBe resp
    empty |+| resp shouldBe resp
  }
  
  it should "be associative" in {
    val a = Response(Event.Foo("a"), Event.Bar(1))
    val b = Response(Event.Foo("b"), Event.Bar(2))
    val c = Response(Event.Foo("c"), Event.Bar(3))
    
    (a |+| b) |+| c shouldBe a |+| (b |+| c)
  }
  
  it should "work with combineAll for multiple responses" in {
    val responses = List(
      Response(Event.Foo("Hello"), Event.Bar(1)),
      Response(Event.Foo(" "), Event.Bar(2)),
      Response(Event.Foo("World"), Event.Bar(3))
    )
    
    val combined = responses.combineAll
    combined shouldBe Response(Event.Foo("Hello World"), Event.Bar(6))
  }
}
