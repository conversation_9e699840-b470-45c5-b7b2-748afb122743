import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import cats.implicits._

class MonoidSpec extends AnyFlatSpec with Matchers {

  "Event.Foo Monoid" should "have empty string as empty element" in {
    import cats.Monoid
    val empty = Monoid[Event.Foo].empty
    empty shouldBe Event.Foo("")
  }

  it should "combine with empty as identity" in {
    import cats.Monoid
    val foo = Event.Foo("hello")
    val empty = Monoid[Event.Foo].empty

    foo |+| empty shouldBe foo
    empty |+| foo shouldBe foo
  }

  it should "combine Foo events by concatenating strings" in {
    val foo1 = Event.Foo("hello")
    val foo2 = Event.Foo(" world")

    foo1 |+| foo2 shouldBe Event.Foo("hello world")
  }

  it should "be associative" in {
    val a = Event.Foo("a")
    val b = Event.Foo("b")
    val c = Event.Foo("c")

    (a |+| b) |+| c shouldBe a |+| (b |+| c)
  }

  "Event.Bar Monoid" should "have zero as empty element" in {
    import cats.Monoid
    val empty = Monoid[Event.Bar].empty
    empty shouldBe Event.Bar(0)
  }

  it should "combine with empty as identity" in {
    import cats.Monoid
    val bar = Event.Bar(42)
    val empty = Monoid[Event.Bar].empty

    bar |+| empty shouldBe bar
    empty |+| bar shouldBe bar
  }

  it should "combine Bar events by adding integers" in {
    val bar1 = Event.Bar(10)
    val bar2 = Event.Bar(32)

    bar1 |+| bar2 shouldBe Event.Bar(42)
  }

  it should "be associative" in {
    val x = Event.Bar(1)
    val y = Event.Bar(2)
    val z = Event.Bar(3)

    (x |+| y) |+| z shouldBe x |+| (y |+| z)
  }
  
  "Response Monoid" should "have empty Response with None values" in {
    import cats.Monoid
    val empty = Monoid[Response].empty
    empty shouldBe Response(None, None)
  }

  it should "combine Responses by combining their Option fields" in {
    val resp1 = Response(Some(Event.Foo("hello")), Some(Event.Bar(10)))
    val resp2 = Response(Some(Event.Foo(" world")), Some(Event.Bar(32)))

    val combined = resp1 |+| resp2
    combined shouldBe Response(Some(Event.Foo("hello world")), Some(Event.Bar(42)))
  }

  it should "work with empty as identity" in {
    import cats.Monoid
    val resp = Response(Some(Event.Foo("test")), Some(Event.Bar(100)))
    val empty = Monoid[Response].empty

    resp |+| empty shouldBe resp
    empty |+| resp shouldBe resp
  }

  it should "handle None values correctly" in {
    val resp1 = Response(Some(Event.Foo("hello")), None)
    val resp2 = Response(None, Some(Event.Bar(42)))
    val resp3 = Response(Some(Event.Foo(" world")), Some(Event.Bar(8)))

    val combined = resp1 |+| resp2 |+| resp3
    combined shouldBe Response(Some(Event.Foo("hello world")), Some(Event.Bar(50)))
  }

  it should "be associative" in {
    val a = Response(Some(Event.Foo("a")), Some(Event.Bar(1)))
    val b = Response(Some(Event.Foo("b")), Some(Event.Bar(2)))
    val c = Response(Some(Event.Foo("c")), Some(Event.Bar(3)))

    (a |+| b) |+| c shouldBe a |+| (b |+| c)
  }

  it should "work with combineAll for multiple responses" in {
    val responses = List(
      Response(Some(Event.Foo("Hello")), Some(Event.Bar(1))),
      Response(Some(Event.Foo(" ")), Some(Event.Bar(2))),
      Response(Some(Event.Foo("World")), Some(Event.Bar(3)))
    )

    val combined = responses.combineAll
    combined shouldBe Response(Some(Event.Foo("Hello World")), Some(Event.Bar(6)))
  }

  it should "work with partial responses in combineAll" in {
    val responses = List(
      Response(Some(Event.Foo("Scala")), None),
      Response(None, Some(Event.Bar(3))),
      Response(Some(Event.Foo(" rocks")), Some(Event.Bar(39)))
    )

    val combined = responses.combineAll
    combined shouldBe Response(Some(Event.Foo("Scala rocks")), Some(Event.Bar(42)))
  }
}
