error id: file://<WORKSPACE>/src/main/scala/Main.scala:`<none>`.
file://<WORKSPACE>/src/main/scala/Main.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:

offset: 473
uri: file://<WORKSPACE>/src/main/scala/Main.scala
text:
```scala
import cats.effect.{IO, IOApp}
import cats.implicits.*
import scala.concurrent.duration._
import cats.effect.std.MapRef

object Main extends IOApp.Simple {
  
  // Example of a simple effectful interpreter
  val simpleInterpreter = new Interpreter {
    def op0(data: String): IO[ApiOneResult] =
      IO.println(s"Processing op0 with: $data") *>
      IO.pure(ApiOneResult(s"Processed: $data"))

    def op1(data: String): IO[ApiTwoResult] =
      IO.println(s"Processing @@op1 with: $data") *>
      IO.pure(ApiTwoResult(data.length))
  }
  
  def run: IO[Unit] = ???
  
  
  
```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.