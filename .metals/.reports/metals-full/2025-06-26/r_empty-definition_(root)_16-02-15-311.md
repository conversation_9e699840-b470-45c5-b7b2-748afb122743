error id: file://<WORKSPACE>/src/main/scala/model.scala:`<none>`.
file://<WORKSPACE>/src/main/scala/model.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:

offset: 1260
uri: file://<WORKSPACE>/src/main/scala/model.scala
text:
```scala
import cats.effect.IO
import cats.implicits._

// Domain models
case class Response(x: String, y: Int)

sealed trait Result
case class ApiOneResult(x: String) extends Result
case class ApiTwoResult(y: Int) extends Result

sealed trait Event
case class Begin(pid: String) extends Event
case class Foo(data: String) extends Event
case class Bar(data: Int) extends Event

enum State:
  case Start, Processing, Stopped

trait Interpreter:
  def op0(data: String): IO[ApiOneResult]
  def op1(data: String): IO[ApiTwoResult]

object ConcreteInterpreter extends Interpreter:
  def op0(data: String): IO[ApiOneResult] =
    IO.pure(ApiOneResult("Hello, World"))

  def op1(data: String): IO[ApiTwoResult] =
    IO.pure(ApiTwoResult(42))

class FSM(interpreter: Interpreter):
  private var state: State = State.Start
 
  def run(event: Event): IO[Response] = 
    (state, event) match
      case (State.Start, Begin(pid)) =>
        // Start event triggers op0 with the pid

      case (State.Processing, Foo(data)) =>
        // Foo event triggers op1 with the data

      case (State.Processing, Bar(data)) =>
        // Bar event sets the state to Stopped

      case (State.Stopped, _) =>
        IO.unit
      case _ =>
        IO.raiseError(new RuntimeException("@@Invalid event for current state"))


```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.